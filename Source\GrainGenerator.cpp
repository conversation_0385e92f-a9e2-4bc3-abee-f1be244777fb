/*
  ==============================================================================

    GrainGenerator.cpp
    Created: 21 May 2023
    Author:  AuraBloom Team

    Implementation of the GrainGenerator class.

  ==============================================================================
*/

#include "GrainGenerator.h"

//==============================================================================
// Grain implementation
//==============================================================================

float Grain::getEnvelopeValue() const
{
    float normalizedAge = getNormalizedAge();

    // 确保normalizedAge在有效范围内
    normalizedAge = juce::jlimit(0.0f, 1.0f, normalizedAge);

    switch (envelopeType)
    {
        case 0: // 超平滑高斯窗 - 专为高Rate优化
        {
            float x = (normalizedAge - 0.5f) * 2.0f;
            // 使用更温和的衰减，确保边缘接近0
            float envelope = std::exp(-x * x * 1.5f);
            // 额外的边缘平滑处理
            if (normalizedAge < 0.1f)
                envelope *= (normalizedAge / 0.1f);
            else if (normalizedAge > 0.9f)
                envelope *= ((1.0f - normalizedAge) / 0.1f);
            return envelope;
        }

        case 1: // 改进的三角窗 - 更平滑的边缘
        {
            float envelope = 1.0f - std::abs(normalizedAge * 2.0f - 1.0f);
            // 边缘软化
            if (normalizedAge < 0.05f)
                envelope *= (normalizedAge / 0.05f);
            else if (normalizedAge > 0.95f)
                envelope *= ((1.0f - normalizedAge) / 0.05f);
            return envelope;
        }

        case 2: // 改进的矩形窗 - 更长的淡入淡出
        {
            float fadeTime = 0.15f; // 增加到15%的淡入淡出时间
            if (normalizedAge < fadeTime)
            {
                // 使用平滑的淡入曲线
                float t = normalizedAge / fadeTime;
                return 0.5f * (1.0f - std::cos(t * juce::MathConstants<float>::pi));
            }
            else if (normalizedAge > 1.0f - fadeTime)
            {
                // 使用平滑的淡出曲线
                float t = (1.0f - normalizedAge) / fadeTime;
                return 0.5f * (1.0f - std::cos(t * juce::MathConstants<float>::pi));
            }
            else
                return 1.0f;
        }

        case 3: // 超级平滑Hann窗 - 彻底消除click
        {
            // 标准Hann窗
            float envelopeValue = 0.5f * (1.0f - std::cos(normalizedAge * juce::MathConstants<float>::twoPi));

            // 额外的边缘超级平滑处理 - 20%边缘区域
            float edgeSmooth = 0.2f;
            if (normalizedAge < edgeSmooth)
            {
                float edgeFactor = normalizedAge / edgeSmooth;
                // 使用三次方程使边缘更平滑
                edgeFactor = edgeFactor * edgeFactor * (3.0f - 2.0f * edgeFactor);
                envelopeValue *= edgeFactor;
            }
            else if (normalizedAge > 1.0f - edgeSmooth)
            {
                float edgeFactor = (1.0f - normalizedAge) / edgeSmooth;
                // 使用三次方程使边缘更平滑
                edgeFactor = edgeFactor * edgeFactor * (3.0f - 2.0f * edgeFactor);
                envelopeValue *= edgeFactor;
            }
            return envelopeValue;
        }

        default:
            // 默认使用Hann窗
            return 0.5f * (1.0f - std::cos(normalizedAge * juce::MathConstants<float>::twoPi));
    }
}

//==============================================================================
// GrainGenerator implementation
//==============================================================================

GrainGenerator::GrainGenerator()
{
}

GrainGenerator::~GrainGenerator()
{
}

void GrainGenerator::initialize(int maxGrains, double sampleRate)
{
    this->sampleRate = sampleRate;

    // Initialize grain pool
    grains.resize(maxGrains);
    for (auto& grain : grains)
    {
        grain.active = false;
    }
}

void GrainGenerator::processAudio(juce::AudioBuffer<float>& outputBuffer, const AudioBufferManager& bufferManager)
{
    const int numSamples = outputBuffer.getNumSamples();

    // Process each active grain
    for (auto& grain : grains)
    {
        if (grain.isActive())
        {
            processGrain(grain, outputBuffer, bufferManager, 0, numSamples);
        }
    }
}

bool GrainGenerator::triggerGrain(int bufferPosition, float duration, float pitch, float pan, int playDirection, int envelopeType)
{
    int grainIndex = findInactiveGrain();

    if (grainIndex >= 0)
    {
        Grain& grain = grains[grainIndex];

        grain.active = true;
        grain.startPosition = bufferPosition;
        grain.age = 0.0f;
        grain.duration = duration;
        grain.pitch = pitch;
        grain.pan = juce::jlimit(-1.0f, 1.0f, pan);
        grain.playDirection = playDirection;
        grain.envelopeType = juce::jlimit(0, 3, envelopeType);

        return true;
    }

    return false;
}

bool GrainGenerator::triggerGrainNormalized(float normalizedPosition, float duration, float pitch, float pan, int playDirection, int envelopeType)
{
    // Convert normalized position to buffer position using actual sample rate
    int bufferPosition = static_cast<int>(normalizedPosition * sampleRate * 5.0f); // 5 seconds buffer

    // 改进的颗粒长度限制 - 允许更长的颗粒以创建连续纹理
    duration = juce::jlimit(0.05f, 0.5f, duration); // 50ms到500ms

    // 更保守的音高限制 - 避免极端变调
    pitch = juce::jlimit(0.8f, 1.2f, pitch); // 0.8到1.2

    return triggerGrain(bufferPosition, duration, pitch, pan, playDirection, envelopeType);
}

void GrainGenerator::setDensity(float density)
{
    // 严格限制密度范围，防止过载
    this->density = juce::jlimit(0.1f, 20.0f, density); // 最大20颗粒/秒
}

void GrainGenerator::update(float deltaTime, const AudioBufferManager& bufferManager)
{
    // Update grain ages
    for (auto& grain : grains)
    {
        if (grain.active)
        {
            grain.age += deltaTime;

            // Deactivate if too old
            if (grain.age >= grain.duration)
            {
                grain.active = false;
            }
        }
    }

    // 改进的颗粒触发策略 - 连续颗粒流
    timeSinceLastGrain += deltaTime;

    // 平滑的颗粒密度控制 - 保持连续性
    int activeGrains = getActiveGrainCount();
    int maxGrains = getMaxGrains();

    // 使用更温和的限制策略，避免突然停止
    float utilizationRatio = static_cast<float>(activeGrains) / maxGrains;

    // FIXED: 移除概率性跳过机制，确保连续的粒子生成
    // 只在真正达到100%使用率时才限制，并且使用更温和的方式
    if (utilizationRatio >= 1.0f)
    {
        // 只有在完全满载时才跳过，确保连续性
        return;
    }

    // 基于活跃颗粒数动态调整密度
    float densityMultiplier = 1.0f - (static_cast<float>(activeGrains) / static_cast<float>(maxGrains));
    float safeDensity = juce::jlimit(0.1f, 10.0f, density * densityMultiplier); // 进一步限制到10颗粒/秒

    float baseGrainRate = juce::jmax(2.0f, safeDensity * 0.3f); // 更保守的密度

    // 颗粒长度：更保守的范围
    float baseDuration = juce::jlimit(0.1f, 0.25f, 0.15f + (safeDensity / 50.0f) * 0.1f);

    // 极高重叠因子以彻底消除click声
    float overlapFactor = 8.0f; // 8倍重叠，确保完全连续性
    float actualInterval = baseDuration / overlapFactor;

    if (timeSinceLastGrain >= actualInterval)
    {
        // 基于缓冲区播放位置的智能位置选择
        float normalizedPosition = bufferManager.getNormalizedPlaybackPosition();

        // 添加适度的位置随机性
        normalizedPosition += (juce::Random::getSystemRandom().nextFloat() - 0.5f) * 0.05f;
        normalizedPosition = juce::jlimit(0.0f, 1.0f, normalizedPosition);

        // 改进的颗粒长度：确保足够长以创建连续纹理
        float duration = baseDuration + juce::Random::getSystemRandom().nextFloat() * 0.05f; // 添加5%随机变化

        // 更保守的音高变化 - 避免极端变调
        float pitch = 0.95f + juce::Random::getSystemRandom().nextFloat() * 0.1f; // 0.95-1.05

        // 更窄的声像范围 - 避免极端声像
        float pan = (juce::Random::getSystemRandom().nextFloat() - 0.5f) * 0.4f; // -0.2到0.2

        // 主要使用前向播放 - 保持音频连贯性
        int playDirection = juce::Random::getSystemRandom().nextFloat() > 0.9f ? -1 : 1;

        // 强制使用Hann窗 - 最平滑，无click声
        int envelopeType = 3; // 100%使用Hann窗

        // 触发颗粒
        triggerGrainNormalized(normalizedPosition, duration, pitch, pan, playDirection, envelopeType);

        // 重置计时器
        timeSinceLastGrain = 0.0f;
    }
}

int GrainGenerator::getActiveGrainCount() const
{
    int count = 0;

    for (const auto& grain : grains)
    {
        if (grain.active)
        {
            count++;
        }
    }

    return count;
}

int GrainGenerator::getMaxGrains() const
{
    return static_cast<int>(grains.size());
}

int GrainGenerator::findInactiveGrain() const
{
    for (int i = 0; i < grains.size(); ++i)
    {
        if (!grains[i].active)
        {
            return i;
        }
    }

    return -1;
}

void GrainGenerator::processGrain(Grain& grain, juce::AudioBuffer<float>& outputBuffer, const AudioBufferManager& bufferManager, int startSample, int numSamples)
{
    const int numChannels = outputBuffer.getNumChannels();

    // Apply a reasonable safety factor to prevent clipping
    int activeCount = getActiveGrainCount();
    float safetyFactor = 0.3f / (1.0f + activeCount * 0.1f); // 合理的安全系数，保证足够音量

    // Calculate pan gains with safety factor applied
    float leftGain = 0.5f * (1.0f - grain.pan) * safetyFactor;
    float rightGain = 0.5f * (1.0f + grain.pan) * safetyFactor;

    // Process samples
    for (int sample = 0; sample < numSamples; ++sample)
    {
        // 为每个样本重新计算颗粒进度和包络值
        float currentAge = grain.age + static_cast<float>(sample) / static_cast<float>(sampleRate);
        float grainProgress = currentAge / grain.duration;

        // Ensure grain progress is within valid range
        if (grainProgress < 0.0f || grainProgress > 1.0f)
            continue;

        // 计算当前样本的包络值
        float currentNormalizedAge = juce::jlimit(0.0f, 1.0f, grainProgress);
        float envelopeValue = 0.0f;

        // 使用改进的包络计算 - 专为消除click声优化
        switch (grain.envelopeType)
        {
            case 0: // 超平滑高斯窗
            {
                float x = (currentNormalizedAge - 0.5f) * 2.0f;
                envelopeValue = std::exp(-x * x * 1.5f);
                // 额外的边缘平滑处理
                if (currentNormalizedAge < 0.1f)
                    envelopeValue *= (currentNormalizedAge / 0.1f);
                else if (currentNormalizedAge > 0.9f)
                    envelopeValue *= ((1.0f - currentNormalizedAge) / 0.1f);
                break;
            }
            case 1: // 改进的三角窗
            {
                envelopeValue = 1.0f - std::abs(currentNormalizedAge * 2.0f - 1.0f);
                // 边缘软化
                if (currentNormalizedAge < 0.05f)
                    envelopeValue *= (currentNormalizedAge / 0.05f);
                else if (currentNormalizedAge > 0.95f)
                    envelopeValue *= ((1.0f - currentNormalizedAge) / 0.05f);
                break;
            }
            case 2: // 改进的矩形窗
            {
                float fadeTime = 0.15f;
                if (currentNormalizedAge < fadeTime)
                {
                    float t = currentNormalizedAge / fadeTime;
                    envelopeValue = 0.5f * (1.0f - std::cos(t * juce::MathConstants<float>::pi));
                }
                else if (currentNormalizedAge > 1.0f - fadeTime)
                {
                    float t = (1.0f - currentNormalizedAge) / fadeTime;
                    envelopeValue = 0.5f * (1.0f - std::cos(t * juce::MathConstants<float>::pi));
                }
                else
                    envelopeValue = 1.0f;
                break;
            }
            case 3: // 超级平滑Hann窗 - 彻底消除click
            {
                // 标准Hann窗
                envelopeValue = 0.5f * (1.0f - std::cos(currentNormalizedAge * juce::MathConstants<float>::twoPi));

                // 额外的边缘超级平滑处理 - 20%边缘区域
                float edgeSmooth = 0.2f;
                if (currentNormalizedAge < edgeSmooth)
                {
                    float edgeFactor = currentNormalizedAge / edgeSmooth;
                    // 使用三次方程使边缘更平滑
                    edgeFactor = edgeFactor * edgeFactor * (3.0f - 2.0f * edgeFactor);
                    envelopeValue *= edgeFactor;
                }
                else if (currentNormalizedAge > 1.0f - edgeSmooth)
                {
                    float edgeFactor = (1.0f - currentNormalizedAge) / edgeSmooth;
                    // 使用三次方程使边缘更平滑
                    edgeFactor = edgeFactor * edgeFactor * (3.0f - 2.0f * edgeFactor);
                    envelopeValue *= edgeFactor;
                }
                break;
            }
            default:
                // 默认使用Hann窗
                envelopeValue = 0.5f * (1.0f - std::cos(currentNormalizedAge * juce::MathConstants<float>::twoPi));
        }

        // 计算读取位置（使用浮点数以支持插值）
        float exactReadPosition = grain.startPosition + grainProgress * grain.duration * sampleRate * grain.pitch;

        // 安全的线性插值读取样本
        float sampleValue = 0.0f;
        try {
            int pos1 = static_cast<int>(exactReadPosition);
            int pos2 = pos1 + 1;
            float frac = exactReadPosition - pos1;

            // 额外的边界检查
            if (pos1 < 0 || pos2 < 0 || frac < 0.0f || frac > 1.0f)
            {
                continue; // 跳过无效位置
            }

            float sample1 = bufferManager.readSample(0, pos1, grain.playDirection);
            float sample2 = bufferManager.readSample(0, pos2, grain.playDirection);

            // 检查样本值是否有效
            if (std::isnan(sample1) || std::isnan(sample2) ||
                std::isinf(sample1) || std::isinf(sample2))
            {
                continue; // 跳过无效样本
            }

            // 线性插值
            sampleValue = sample1 + frac * (sample2 - sample1);

            // 最终安全检查
            if (std::isnan(sampleValue) || std::isinf(sampleValue))
            {
                sampleValue = 0.0f;
            }
        }
        catch (...) {
            // If any exception occurs, skip this sample
            continue;
        }

        // Apply envelope
        sampleValue *= envelopeValue;

        // 移除静态变量，避免多线程问题
        // 直接使用更温和的处理

        // 只在真正需要时才限制
        if (std::abs(sampleValue) > 0.9f)
        {
            sampleValue = std::tanh(sampleValue * 0.8f); // 轻微限制
        }

        // Apply to output channels with bounds checking
        if (numChannels >= 1 && startSample + sample < outputBuffer.getNumSamples())
        {
            outputBuffer.addSample(0, startSample + sample, sampleValue * leftGain);
        }

        if (numChannels >= 2 && startSample + sample < outputBuffer.getNumSamples())
        {
            outputBuffer.addSample(1, startSample + sample, sampleValue * rightGain);
        }
    }
}

void GrainGenerator::clearAllGrains()
{
    for (auto& grain : grains)
    {
        grain.active = false;
        grain.age = 0.0f;
    }
}
