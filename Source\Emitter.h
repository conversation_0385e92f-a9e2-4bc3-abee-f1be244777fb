/*
  ==============================================================================

    Emitter.h
    Created: 21 May 2023
    Author:  AuraBloom Team

    This file defines the Emitter class, which is responsible for generating
    particles in the particle system.

  ==============================================================================
*/

#pragma once

#include <JuceHeader.h>
#include "ParticleSystem.h"

class Emitter
{
public:
    Emitter();
    ~Emitter();
    
    // Initialize the emitter
    void initialize(ParticleSystem* particleSystem);
    
    // Update the emitter
    void update(float deltaTime);
    
    // Set emitter position
    void setPosition(float x, float y);
    
    // Set emitter shape
    enum Shape
    {
        Point,
        Line,
        Circle
    };
    
    void setShape(Shape shape);
    
    // Set emission rate (particles per second)
    void setRate(float rate);
    
    // Set burst mode
    void setBurstMode(bool burstMode);
    
    // Set initial velocity
    void setInitialVelocity(float velocity);
    
    // Set spread
    void setSpread(float spread);
    
    // Set audio input
    void setAudioInput(int inputIndex);
    
    // Set buffer playback position
    void setBufferPlaybackPosition(float position);
    
    // Set buffer scan speed
    void setBufferScanSpeed(float speed);
    
    // Set buffer loop mode
    enum LoopMode
    {
        NoLoop,
        Forward,
        PingPong
    };
    
    void setBufferLoopMode(LoopMode mode);
    
private:
    // Reference to particle system
    ParticleSystem* particleSystem = nullptr;
    
    // Emitter properties
    float x = 0.0f;
    float y = 0.0f;
    Shape shape = Point;
    float rate = 10.0f;
    bool burstMode = false;
    float initialVelocity = 50.0f;
    float spread = 50.0f;
    
    // Audio properties
    int audioInput = 0;
    float bufferPlaybackPosition = 0.0f;
    float bufferScanSpeed = 0.0f;
    LoopMode bufferLoopMode = NoLoop;
    
    // Internal state
    float timeSinceLastEmission = 0.0f;
    
    // Emit a particle
    void emitParticle();
    
    // Calculate emission interval
    float getEmissionInterval() const;
};
