#include "PerformanceMonitor.h"

PerformanceMonitor::PerformanceMonitor()
{
    // std::deque doesn't have reserve(), but that's okay
    resetStats();
}

PerformanceMonitor::~PerformanceMonitor()
{
}

void PerformanceMonitor::startFrame()
{
    frameStartTime = std::chrono::high_resolution_clock::now();
}

void PerformanceMonitor::endFrame()
{
    auto frameEndTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(frameEndTime - frameStartTime);
    float frameTimeMs = duration.count() / 1000.0f;

    updateFrameTimeHistory(frameTimeMs);
    calculateCpuUsage();

    if (adaptiveMode)
    {
        updateAdaptiveSettings();
    }

    // 更新统计信息
    stats.minFrameTime = frameCount == 0 ? frameTimeMs : juce::jmin(stats.minFrameTime, frameTimeMs);
    stats.maxFrameTime = juce::jmax(stats.maxFrameTime, frameTimeMs);

    if (frameTimeMs > maxFrameTime)
    {
        stats.droppedFrames++;
    }

    frameCount++;
}

void PerformanceMonitor::updateMetrics(int activeParticles, int totalParticles)
{
    this->activeParticles = activeParticles;
    this->totalParticles = totalParticles;

    stats.peakParticles = juce::jmax(stats.peakParticles, activeParticles);

    updateMemoryUsage();
}

bool PerformanceMonitor::shouldReduceComplexity() const
{
    return cpuUsage > targetCpuUsage || averageFrameTime > maxFrameTime;
}

bool PerformanceMonitor::shouldLimitParticles() const
{
    return activeParticles > recommendedMaxParticles || shouldReduceComplexity();
}

int PerformanceMonitor::getRecommendedMaxParticles() const
{
    return recommendedMaxParticles;
}

float PerformanceMonitor::getComplexityReduction() const
{
    return complexityReduction;
}

void PerformanceMonitor::resetStats()
{
    stats = Stats();
    frameCount = 0;
    frameTimeHistory.clear();
}

void PerformanceMonitor::updateFrameTimeHistory(float frameTime)
{
    frameTimeHistory.push_back(frameTime);

    if (frameTimeHistory.size() > MAX_HISTORY_SIZE)
    {
        frameTimeHistory.pop_front();
    }

    // 计算平均帧时间
    if (!frameTimeHistory.empty())
    {
        float sum = 0.0f;
        for (float time : frameTimeHistory)
        {
            sum += time;
        }
        averageFrameTime = sum / frameTimeHistory.size();
        stats.avgFrameTime = averageFrameTime;
    }
}

void PerformanceMonitor::calculateCpuUsage()
{
    if (averageFrameTime > 0.0f)
    {
        // 估算CPU使用率：实际帧时间 / 目标帧时间 * 100%
        float targetFrameTime = 1000.0f / 60.0f; // 60fps = 16.67ms
        cpuUsage = (averageFrameTime / targetFrameTime) * 100.0f;
        cpuUsage = juce::jlimit(0.0f, 100.0f, cpuUsage);
        stats.cpuUsage = cpuUsage;
    }
}

void PerformanceMonitor::updateAdaptiveSettings()
{
    // 自适应复杂度控制
    if (cpuUsage > targetCpuUsage * 1.2f) // 超过目标20%
    {
        complexityReduction *= 0.9f; // 减少10%复杂度
        recommendedMaxParticles = static_cast<int>(recommendedMaxParticles * 0.9f);
    }
    else if (cpuUsage < targetCpuUsage * 0.8f) // 低于目标20%
    {
        complexityReduction *= 1.05f; // 增加5%复杂度
        recommendedMaxParticles = static_cast<int>(recommendedMaxParticles * 1.05f);
    }

    // 限制范围
    complexityReduction = juce::jlimit(0.1f, 1.0f, complexityReduction);
    recommendedMaxParticles = juce::jlimit(50, 2000, recommendedMaxParticles);
}

void PerformanceMonitor::updateMemoryUsage()
{
    // 估算内存使用（简化版本）
    // 实际实现中可以使用更精确的内存监控
    float particleMemory = totalParticles * sizeof(float) * 20; // 估算每个粒子20个float
    float bufferMemory = 44100 * 2 * 5 * sizeof(float); // 5秒立体声缓冲区

    memoryUsageMB = (particleMemory + bufferMemory) / (1024.0f * 1024.0f);
}
