@echo off
REM AuraBloom Windows Build Script
REM 使用方法: build_windows.bat [Debug|Release] [x64|Win32]

echo ========================================
echo AuraBloom Windows Build Script
echo ========================================

REM 设置默认参数
set BUILD_CONFIG=Release
set PLATFORM=x64

REM 解析命令行参数
if not "%1"=="" set BUILD_CONFIG=%1
if not "%2"=="" set PLATFORM=%2

echo Build Configuration: %BUILD_CONFIG%
echo Platform: %PLATFORM%
echo.

REM 检查Visual Studio安装
set VS_PATH=""
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set VS_PATH="C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    set VS_PATH="C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe"
) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe" (
    set VS_PATH="C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe"
) else (
    echo ERROR: Visual Studio 2022 not found!
    echo Please install Visual Studio 2022 with C++ development tools.
    pause
    exit /b 1
)

echo Found Visual Studio at: %VS_PATH%
echo.

REM 检查解决方案文件
if not exist "Builds\VisualStudio2022\AuraBloom.sln" (
    echo ERROR: Solution file not found!
    echo Please ensure you are running this script from the AuraBloom project root.
    pause
    exit /b 1
)

echo Building AuraBloom VST3 Plugin...
echo.

REM 清理之前的构建
echo Cleaning previous build...
%VS_PATH% "Builds\VisualStudio2022\AuraBloom.sln" /t:Clean /p:Configuration=%BUILD_CONFIG% /p:Platform=%PLATFORM%

REM 构建项目
echo Building project...
%VS_PATH% "Builds\VisualStudio2022\AuraBloom.sln" /t:AuraBloom_VST3 /p:Configuration=%BUILD_CONFIG% /p:Platform=%PLATFORM% /m

REM 检查构建结果
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo BUILD SUCCESSFUL!
    echo ========================================
    echo.
    echo Plugin location:
    echo Builds\VisualStudio2022\%PLATFORM%\%BUILD_CONFIG%\VST3\AuraBloom.vst3
    echo.
    echo To install the plugin:
    echo 1. Copy the AuraBloom.vst3 folder to:
    echo    C:\Program Files\Common Files\VST3\
    echo 2. Restart your DAW
    echo 3. Rescan plugins
    echo.
    
    REM 询问是否自动安装
    set /p INSTALL_CHOICE="Do you want to automatically install the plugin? (y/n): "
    if /i "%INSTALL_CHOICE%"=="y" (
        echo Installing plugin...
        if exist "C:\Program Files\Common Files\VST3\" (
            xcopy "Builds\VisualStudio2022\%PLATFORM%\%BUILD_CONFIG%\VST3\AuraBloom.vst3" "C:\Program Files\Common Files\VST3\AuraBloom.vst3" /E /I /Y
            if %ERRORLEVEL% EQU 0 (
                echo Plugin installed successfully!
            ) else (
                echo Installation failed. Please copy manually with administrator privileges.
            )
        ) else (
            echo VST3 directory not found. Please install manually.
        )
    )
) else (
    echo.
    echo ========================================
    echo BUILD FAILED!
    echo ========================================
    echo.
    echo Please check the error messages above.
    echo Common issues:
    echo 1. Missing JUCE framework
    echo 2. Incorrect Visual Studio version
    echo 3. Missing Windows SDK
    echo 4. Project configuration issues
    echo.
    echo See Windows_Build_Instructions.md for detailed troubleshooting.
)

echo.
pause
