/*
  ==============================================================================

    Particle.h
    Created: 21 May 2023
    Author:  AuraBloom Team

    This file defines the Particle class, which represents a single particle
    in the particle system.

  ==============================================================================
*/

#pragma once

#include <JuceHeader.h>
#include <array>

// Forward declaration
class ParticleSystem;

/**
 * Enum defining different particle states
 */
enum class ParticleState
{
    Inactive,   // Particle is not active
    Active,     // Particle is active and normal
    Excited,    // Particle is in an excited state (higher energy)
    Decaying,   // Particle is in a decaying state (losing energy)
    Transforming // Particle is transforming to another state
};

/**
 * Enum defining different particle types
 */
enum class ParticleType
{
    Normal,     // Standard particle
    Attractor,  // Attracts other particles
    Repeller,   // Repels other particles
    Oscillator, // Oscillates between states
    Catalyst    // Affects behavior of nearby particles
};

/**
 * Class representing a single particle in the particle system
 */
class Particle
{
public:
    Particle();
    ~Particle();

    // Position in 2D space
    float x = 0.0f;
    float y = 0.0f;

    // Velocity
    float vx = 0.0f;
    float vy = 0.0f;

    // Acceleration
    float ax = 0.0f;
    float ay = 0.0f;

    // Physical properties
    float mass = 1.0f;              // Mass affects force response
    float charge = 0.0f;            // Electrical charge (-1.0 to 1.0)
    float energy = 1.0f;            // Energy level (0.0 to 1.0)
    float radius = 5.0f;            // Collision radius

    // Audio properties
    float grainSize = 50.0f;        // in milliseconds
    float pitchShift = 0.0f;        // in semitones
    float pan = 0.0f;               // -1.0 to 1.0
    int playDirection = 0;          // 0: forward, 1: backward, 2: random

    // Grain envelope
    int envelopeType = 0;           // 0: Gaussian, 1: Triangle, 2: Rectangle, 3: Cosine

    // Particle properties
    float lifespan = 2.0f;          // in seconds
    float age = 0.0f;               // current age in seconds
    bool isActive = false;          // whether the particle is active

    // State and type
    ParticleState state = ParticleState::Inactive;
    ParticleType type = ParticleType::Normal;

    // Buffer position
    int bufferPosition = 0;         // current position in the audio buffer
    float bufferPlaybackRate = 1.0f; // playback rate (affected by pitch shift)

    // History tracking
    static constexpr int HISTORY_SIZE = 10;
    std::array<juce::Point<float>, HISTORY_SIZE> positionHistory;
    int historyIndex = 0;

    // Update particle state
    void update(float deltaTime);

    // Check if particle is alive
    bool isAlive() const;

    // Get normalized age (0.0 to 1.0)
    float getNormalizedAge() const;

    // Apply force to particle
    void applyForce(float fx, float fy);

    // Apply force with mass consideration
    void applyForceWithMass(float fx, float fy);

    // Set particle state
    void setState(ParticleState newState);

    // Set particle type
    void setType(ParticleType newType);

    // Calculate distance to another particle
    float distanceTo(const Particle& other) const;

    // Check if colliding with another particle
    bool isCollidingWith(const Particle& other) const;

    // Handle collision with another particle
    void handleCollision(Particle& other);

    // Handle collision with boundaries
    void handleBoundaryCollision(float left, float right, float top, float bottom, float elasticity = 0.8f);

    // Update position history
    void updateHistory();

    // Get position history
    const std::array<juce::Point<float>, HISTORY_SIZE>& getPositionHistory() const;

    // Reset particle
    void reset();

    // Emit child particles
    void emitChildParticles(ParticleSystem& system, int count);

private:
    // Internal methods
    void updateBufferPosition(float deltaTime);

    // Update behavior based on state
    void updateStateEffects(float deltaTime);

    // Update behavior based on type
    void updateTypeEffects(float deltaTime);
};
