/*
  ==============================================================================

    LFOComponent.h
    Created: 21 May 2023
    Author:  AuraBloom Team

    This file defines the LFOComponent class, which is used to display and
    control an LFO.

  ==============================================================================
*/

#pragma once

#include <JuceHeader.h>
#include "ModulationSourceComponent.h"
#include "LFO.h"

/**
 * Component for displaying and controlling an LFO.
 */
class LFOComponent : public ModulationSourceComponent
{
public:
    /**
     * Constructor.
     * 
     * @param lfo The LFO to display and control
     */
    LFOComponent(LFO* lfo);
    
    /**
     * Destructor.
     */
    ~LFOComponent() override;
    
    /**
     * Handle component resize.
     */
    void resized() override;
    
protected:
    /**
     * Draw the LFO controls.
     * 
     * @param g The graphics context
     * @param bounds The bounds to draw in
     */
    void drawControls(juce::Graphics& g, juce::Rectangle<int> bounds) override;
    
private:
    // Get the LFO
    LFO* getLFO() const;
    
    // UI Components
    juce::ComboBox waveformComboBox;
    juce::Slider frequencySlider;
    juce::Slider depthSlider;
    juce::Slider phaseSlider;
    juce::ToggleButton bipolarToggle;
    juce::ComboBox syncModeComboBox;
    
    // Labels
    juce::Label waveformLabel;
    juce::Label frequencyLabel;
    juce::Label depthLabel;
    juce::Label phaseLabel;
    juce::Label bipolarLabel;
    juce::Label syncModeLabel;
    
    // Helper methods
    void setupComboBox(juce::ComboBox& comboBox, juce::Label& label, const juce::String& labelText);
    void setupSlider(juce::Slider& slider, juce::Label& label, const juce::String& labelText);
    void setupToggle(juce::ToggleButton& toggle, juce::Label& label, const juce::String& labelText);
    
    // Listeners
    void waveformChanged();
    void frequencyChanged();
    void depthChanged();
    void phaseChanged();
    void bipolarChanged();
    void syncModeChanged();
    
    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(LFOComponent)
};
