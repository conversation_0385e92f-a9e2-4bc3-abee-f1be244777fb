<?xml version="1.0" encoding="UTF-8"?>

<Project DefaultTargets="Build"
         ToolsVersion="17.0"
         xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{7EC96809-B829-C929-A5FA-496BC2B8C258}</ProjectGuid>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props"/>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'"
                 Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <WholeProgramOptimization>false</WholeProgramOptimization>
    <PlatformToolset>v143</PlatformToolset>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'"
                 Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <PlatformToolset>v143</PlatformToolset>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props"/>
  <ImportGroup Label="ExtensionSettings"/>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props"
            Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')"
            Label="LocalAppDataPlatform"/>
  </ImportGroup>
  <PropertyGroup>
    <_ProjectFileVersion>10.0.30319.1</_ProjectFileVersion>
    <TargetExt>.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(SolutionDir)$(Platform)\$(Configuration)\Shared Code\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\Shared Code\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">AuraBloom</TargetName>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <PreBuildEventUseInBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</PreBuildEventUseInBuild>
    <PostBuildEventUseInBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</PostBuildEventUseInBuild>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(SolutionDir)$(Platform)\$(Configuration)\Shared Code\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\Shared Code\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">AuraBloom</TargetName>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <PreBuildEventUseInBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</PreBuildEventUseInBuild>
    <PostBuildEventUseInBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</PostBuildEventUseInBuild>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <DebugInformationFormat>OldStyle</DebugInformationFormat>
      <AdditionalIncludeDirectories>..\..\JuceLibraryCode;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_CRT_SECURE_NO_WARNINGS;WIN32;_WINDOWS;DEBUG;_DEBUG;JUCE_PROJUCER_VERSION=0x80007;JUCE_MODULE_AVAILABLE_juce_audio_basics=1;JUCE_MODULE_AVAILABLE_juce_audio_devices=1;JUCE_MODULE_AVAILABLE_juce_audio_formats=1;JUCE_MODULE_AVAILABLE_juce_audio_plugin_client=1;JUCE_MODULE_AVAILABLE_juce_audio_processors=1;JUCE_MODULE_AVAILABLE_juce_audio_utils=1;JUCE_MODULE_AVAILABLE_juce_core=1;JUCE_MODULE_AVAILABLE_juce_data_structures=1;JUCE_MODULE_AVAILABLE_juce_dsp=1;JUCE_MODULE_AVAILABLE_juce_events=1;JUCE_MODULE_AVAILABLE_juce_graphics=1;JUCE_MODULE_AVAILABLE_juce_gui_basics=1;JUCE_MODULE_AVAILABLE_juce_gui_extra=1;JUCE_MODULE_AVAILABLE_juce_javascript=1;JUCE_MODULE_AVAILABLE_juce_opengl=1;JUCE_MODULE_AVAILABLE_juce_osc=1;JUCE_GLOBAL_MODULE_SETTINGS_INCLUDED=1;JUCE_VST3_CAN_REPLACE_VST2=0;JUCE_PLUGINHOST_VST=0;JUCE_PLUGINHOST_VST3=1;JUCE_PLUGINHOST_AU=1;JUCE_PLUGINHOST_LADSPA=0;JUCE_PLUGINHOST_LV2=0;JUCE_USE_CURL=0;JUCE_LOAD_CURL_SYMBOLS_LAZILY=1;JUCE_STRICT_REFCOUNTEDPOINTER=1;JUCE_ENABLE_REPAINT_DEBUGGING=0;JUCE_USE_XRANDR=1;JUCE_USE_XINERAMA=1;JUCE_USE_XSHM=1;JUCE_USE_XRENDER=0;JUCE_USE_XCURSOR=1;JUCE_WEB_BROWSER=0;JUCE_ENABLE_LIVE_CONSTANT_EDITOR=0;JucePlugin_Build_VST=0;JucePlugin_Build_VST3=1;JucePlugin_Build_AU=0;JucePlugin_Build_AUv3=0;JucePlugin_Build_AAX=0;JucePlugin_Build_Standalone=1;JucePlugin_Build_Unity=0;JucePlugin_Build_LV2=0;JucePlugin_Enable_IAA=0;JucePlugin_Enable_ARA=0;JucePlugin_Name=&quot;AuraBloom&quot;;JucePlugin_Desc=&quot;Granular synthesis plugin with particle effects&quot;;JucePlugin_Manufacturer=&quot;YourCompany&quot;;JucePlugin_ManufacturerWebsite=&quot;www.yourcompany.com&quot;;JucePlugin_ManufacturerEmail=&quot;<EMAIL>&quot;;JucePlugin_ManufacturerCode=0x5972636d;JucePlugin_PluginCode=0x41757262;JucePlugin_IsSynth=0;JucePlugin_WantsMidiInput=0;JucePlugin_ProducesMidiOutput=0;JucePlugin_IsMidiEffect=0;JucePlugin_EditorRequiresKeyboardFocus=0;JucePlugin_Version=1.0.0;JucePlugin_VersionCode=0x10000;JucePlugin_VersionString=&quot;1.0.0&quot;;JucePlugin_VSTUniqueID=JucePlugin_PluginCode;JucePlugin_VSTCategory=kPlugCategEffect;JucePlugin_Vst3Category=&quot;Fx&quot;;JucePlugin_AUMainType='aufx';JucePlugin_AUSubType=JucePlugin_PluginCode;JucePlugin_AUExportPrefix=AuraBloomAU;JucePlugin_AUExportPrefixQuoted=&quot;AuraBloomAU&quot;;JucePlugin_AUManufacturerCode=JucePlugin_ManufacturerCode;JucePlugin_CFBundleIdentifier=com.YourCompany.AuraBloom;JucePlugin_AAXIdentifier=com.yourcompany.aurabloom;JucePlugin_AAXManufacturerCode=JucePlugin_ManufacturerCode;JucePlugin_AAXProductId=JucePlugin_PluginCode;JucePlugin_AAXCategory=2;JucePlugin_AAXDisableBypass=0;JucePlugin_AAXDisableMultiMono=0;JucePlugin_IAAType=0x61757278;JucePlugin_IAASubType=JucePlugin_PluginCode;JucePlugin_IAAName=&quot;YourCompany: AuraBloom&quot;;JucePlugin_VSTNumMidiInputs=16;JucePlugin_VSTNumMidiOutputs=16;JucePlugin_ARAContentTypes=0;JucePlugin_ARATransformationFlags=0;JucePlugin_ARAFactoryID=&quot;com.YourCompany.AuraBloom.factory&quot;;JucePlugin_ARADocumentArchiveID=&quot;com.YourCompany.AuraBloom.aradocumentarchive.1.0.0&quot;;JucePlugin_ARACompatibleArchiveIDs=&quot;&quot;;JucePlugin_MaxNumInputChannels=2;JucePlugin_MaxNumOutputChannels=2;JucePlugin_PreferredChannelConfigurations={1, 1}, {2, 2};JUCE_STANDALONE_APPLICATION=JucePlugin_Build_Standalone;JUCER_VS2022_78A503E=1;JUCE_APP_VERSION=1.0.0;JUCE_APP_VERSION_HEX=0x10000;JUCE_SHARED_CODE=1;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <AssemblerListingLocation>$(IntDir)\</AssemblerListingLocation>
      <ObjectFileName>$(IntDir)\</ObjectFileName>
      <ProgramDataBaseFileName>$(IntDir)\AuraBloom.pdb</ProgramDataBaseFileName>
      <WarningLevel>Level4</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <Optimization>Full</Optimization>
      <DebugInformationFormat>OldStyle</DebugInformationFormat>
      <AdditionalIncludeDirectories>..\..\JuceLibraryCode;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_CRT_SECURE_NO_WARNINGS;WIN32;_WINDOWS;NDEBUG;JUCE_PROJUCER_VERSION=0x80007;JUCE_MODULE_AVAILABLE_juce_audio_basics=1;JUCE_MODULE_AVAILABLE_juce_audio_devices=1;JUCE_MODULE_AVAILABLE_juce_audio_formats=1;JUCE_MODULE_AVAILABLE_juce_audio_plugin_client=1;JUCE_MODULE_AVAILABLE_juce_audio_processors=1;JUCE_MODULE_AVAILABLE_juce_audio_utils=1;JUCE_MODULE_AVAILABLE_juce_core=1;JUCE_MODULE_AVAILABLE_juce_data_structures=1;JUCE_MODULE_AVAILABLE_juce_dsp=1;JUCE_MODULE_AVAILABLE_juce_events=1;JUCE_MODULE_AVAILABLE_juce_graphics=1;JUCE_MODULE_AVAILABLE_juce_gui_basics=1;JUCE_MODULE_AVAILABLE_juce_gui_extra=1;JUCE_MODULE_AVAILABLE_juce_javascript=1;JUCE_MODULE_AVAILABLE_juce_opengl=1;JUCE_MODULE_AVAILABLE_juce_osc=1;JUCE_GLOBAL_MODULE_SETTINGS_INCLUDED=1;JUCE_VST3_CAN_REPLACE_VST2=0;JUCE_PLUGINHOST_VST=0;JUCE_PLUGINHOST_VST3=1;JUCE_PLUGINHOST_AU=1;JUCE_PLUGINHOST_LADSPA=0;JUCE_PLUGINHOST_LV2=0;JUCE_USE_CURL=0;JUCE_LOAD_CURL_SYMBOLS_LAZILY=1;JUCE_STRICT_REFCOUNTEDPOINTER=1;JUCE_ENABLE_REPAINT_DEBUGGING=0;JUCE_USE_XRANDR=1;JUCE_USE_XINERAMA=1;JUCE_USE_XSHM=1;JUCE_USE_XRENDER=0;JUCE_USE_XCURSOR=1;JUCE_WEB_BROWSER=0;JUCE_ENABLE_LIVE_CONSTANT_EDITOR=0;JucePlugin_Build_VST=0;JucePlugin_Build_VST3=1;JucePlugin_Build_AU=0;JucePlugin_Build_AUv3=0;JucePlugin_Build_AAX=0;JucePlugin_Build_Standalone=1;JucePlugin_Build_Unity=0;JucePlugin_Build_LV2=0;JucePlugin_Enable_IAA=0;JucePlugin_Enable_ARA=0;JucePlugin_Name=&quot;AuraBloom&quot;;JucePlugin_Desc=&quot;Granular synthesis plugin with particle effects&quot;;JucePlugin_Manufacturer=&quot;YourCompany&quot;;JucePlugin_ManufacturerWebsite=&quot;www.yourcompany.com&quot;;JucePlugin_ManufacturerEmail=&quot;<EMAIL>&quot;;JucePlugin_ManufacturerCode=0x5972636d;JucePlugin_PluginCode=0x41757262;JucePlugin_IsSynth=0;JucePlugin_WantsMidiInput=0;JucePlugin_ProducesMidiOutput=0;JucePlugin_IsMidiEffect=0;JucePlugin_EditorRequiresKeyboardFocus=0;JucePlugin_Version=1.0.0;JucePlugin_VersionCode=0x10000;JucePlugin_VersionString=&quot;1.0.0&quot;;JucePlugin_VSTUniqueID=JucePlugin_PluginCode;JucePlugin_VSTCategory=kPlugCategEffect;JucePlugin_Vst3Category=&quot;Fx&quot;;JucePlugin_AUMainType='aufx';JucePlugin_AUSubType=JucePlugin_PluginCode;JucePlugin_AUExportPrefix=AuraBloomAU;JucePlugin_AUExportPrefixQuoted=&quot;AuraBloomAU&quot;;JucePlugin_AUManufacturerCode=JucePlugin_ManufacturerCode;JucePlugin_CFBundleIdentifier=com.YourCompany.AuraBloom;JucePlugin_AAXIdentifier=com.yourcompany.aurabloom;JucePlugin_AAXManufacturerCode=JucePlugin_ManufacturerCode;JucePlugin_AAXProductId=JucePlugin_PluginCode;JucePlugin_AAXCategory=2;JucePlugin_AAXDisableBypass=0;JucePlugin_AAXDisableMultiMono=0;JucePlugin_IAAType=0x61757278;JucePlugin_IAASubType=JucePlugin_PluginCode;JucePlugin_IAAName=&quot;YourCompany: AuraBloom&quot;;JucePlugin_VSTNumMidiInputs=16;JucePlugin_VSTNumMidiOutputs=16;JucePlugin_ARAContentTypes=0;JucePlugin_ARATransformationFlags=0;JucePlugin_ARAFactoryID=&quot;com.YourCompany.AuraBloom.factory&quot;;JucePlugin_ARADocumentArchiveID=&quot;com.YourCompany.AuraBloom.aradocumentarchive.1.0.0&quot;;JucePlugin_ARACompatibleArchiveIDs=&quot;&quot;;JucePlugin_MaxNumInputChannels=2;JucePlugin_MaxNumOutputChannels=2;JucePlugin_PreferredChannelConfigurations={1, 1}, {2, 2};JUCE_STANDALONE_APPLICATION=JucePlugin_Build_Standalone;JUCER_VS2022_78A503E=1;JUCE_APP_VERSION=1.0.0;JUCE_APP_VERSION_HEX=0x10000;JUCE_SHARED_CODE=1;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <AssemblerListingLocation>$(IntDir)\</AssemblerListingLocation>
      <ObjectFileName>$(IntDir)\</ObjectFileName>
      <ProgramDataBaseFileName>$(IntDir)\AuraBloom.pdb</ProgramDataBaseFileName>
      <WarningLevel>Level4</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemGroup>
    <!-- Source Files -->
    <ClCompile Include="..\..\Source\PluginProcessor.cpp"/>
    <ClCompile Include="..\..\Source\PluginEditor.cpp"/>
    <ClCompile Include="..\..\Source\Particle.cpp"/>
    <ClCompile Include="..\..\Source\ParticleSystem.cpp"/>
    <ClCompile Include="..\..\Source\Emitter.cpp"/>
    <ClCompile Include="..\..\Source\BloomChamberComponent.cpp"/>
    <ClCompile Include="..\..\Source\AudioBufferManager.cpp"/>
    <ClCompile Include="..\..\Source\GrainGenerator.cpp"/>
    <ClCompile Include="..\..\Source\ModulationSource.cpp"/>
    <ClCompile Include="..\..\Source\LFO.cpp"/>
    <ClCompile Include="..\..\Source\Envelope.cpp"/>
    <ClCompile Include="..\..\Source\ModulationTarget.cpp"/>
    <ClCompile Include="..\..\Source\ParameterModulationTarget.cpp"/>
    <ClCompile Include="..\..\Source\ModulationMatrix.cpp"/>
    <ClCompile Include="..\..\Source\ModulationSourceComponent.cpp"/>
    <ClCompile Include="..\..\Source\LFOComponent.cpp"/>
    <ClCompile Include="..\..\Source\EnvelopeComponent.cpp"/>
    <ClCompile Include="..\..\Source\ModulationMatrixComponent.cpp"/>
    <ClCompile Include="..\..\Source\XYControlPad.cpp"/>
    <ClCompile Include="..\..\Source\MacroControlKnob.cpp"/>
    <ClCompile Include="..\..\Source\MacroControlPanel.cpp"/>
    <ClCompile Include="..\..\Source\PerformanceMonitor.cpp"/>
    
    <!-- JUCE Library Code -->
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_audio_basics.cpp"/>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_audio_devices.cpp"/>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_audio_formats.cpp"/>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_audio_plugin_client_ARA.cpp"/>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_audio_processors.cpp">
      <AdditionalOptions> /bigobj %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_audio_processors_ara.cpp"/>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_audio_processors_lv2_libs.cpp"/>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_audio_utils.cpp"/>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_core.cpp">
      <AdditionalOptions> /bigobj %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_core_CompilationTime.cpp"/>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_data_structures.cpp"/>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_dsp.cpp"/>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_events.cpp"/>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_graphics.cpp">
      <AdditionalOptions> /bigobj %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_graphics_Harfbuzz.cpp"/>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_graphics_Sheenbidi.c"/>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_gui_basics.cpp">
      <AdditionalOptions> /bigobj %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_gui_extra.cpp"/>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_javascript.cpp"/>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_opengl.cpp"/>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_osc.cpp"/>
  </ItemGroup>
  <ItemGroup>
    <!-- Header Files -->
    <ClInclude Include="Source\PluginProcessor.h"/>
    <ClInclude Include="Source\PluginEditor.h"/>
    <ClInclude Include="Source\PluginParameters.h"/>
    <ClInclude Include="Source\Particle.h"/>
    <ClInclude Include="Source\ParticleSystem.h"/>
    <ClInclude Include="Source\Emitter.h"/>
    <ClInclude Include="Source\BloomChamberComponent.h"/>
    <ClInclude Include="Source\AudioBufferManager.h"/>
    <ClInclude Include="Source\GrainGenerator.h"/>
    <ClInclude Include="Source\ForceField.h"/>
    <ClInclude Include="Source\ModulationSource.h"/>
    <ClInclude Include="Source\LFO.h"/>
    <ClInclude Include="Source\Envelope.h"/>
    <ClInclude Include="Source\ModulationTarget.h"/>
    <ClInclude Include="Source\ParameterModulationTarget.h"/>
    <ClInclude Include="Source\ModulationMatrix.h"/>
    <ClInclude Include="Source\AuraBloomLookAndFeel.h"/>
    <ClInclude Include="Source\ModulationSourceComponent.h"/>
    <ClInclude Include="Source\LFOComponent.h"/>
    <ClInclude Include="Source\EnvelopeComponent.h"/>
    <ClInclude Include="Source\ModulationMatrixComponent.h"/>
    <ClInclude Include="Source\XYControlPad.h"/>
    <ClInclude Include="Source\MacroControlKnob.h"/>
    <ClInclude Include="Source\MacroControlPanel.h"/>
    <ClInclude Include="Source\PerformanceMonitor.h"/>
    
    <!-- JUCE Headers -->
    <ClInclude Include="JuceLibraryCode\JuceHeader.h"/>
    <ClInclude Include="JuceLibraryCode\JucePluginDefines.h"/>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets"/>
</Project>
