/*
  ==============================================================================

    ModulationSourceComponent.h
    Created: 21 May 2023
    Author:  AuraBloom Team

    This file defines the ModulationSourceComponent class, which is the base
    class for all modulation source UI components.

  ==============================================================================
*/

#pragma once

#include <JuceHeader.h>
#include "ModulationSource.h"

/**
 * Base class for all modulation source UI components.
 */
class ModulationSourceComponent : public juce::Component,
                                 private juce::Timer
{
public:
    /**
     * Constructor.
     * 
     * @param source The modulation source to display and control
     */
    ModulationSourceComponent(ModulationSource* source);
    
    /**
     * Destructor.
     */
    ~ModulationSourceComponent() override;
    
    /**
     * Paint the component.
     */
    void paint(juce::Graphics& g) override;
    
    /**
     * Handle component resize.
     */
    void resized() override;
    
    /**
     * Get the modulation source.
     * 
     * @return The modulation source
     */
    ModulationSource* getModulationSource() const;
    
    /**
     * Set the modulation source.
     * 
     * @param source The modulation source
     */
    void setModulationSource(ModulationSource* source);
    
    /**
     * Set whether the component is active.
     * 
     * @param active True if the component should be active
     */
    void setActive(bool active);
    
    /**
     * Get whether the component is active.
     * 
     * @return True if the component is active
     */
    bool isActive() const;
    
protected:
    /**
     * Draw the modulation source waveform.
     * 
     * @param g The graphics context
     * @param bounds The bounds to draw in
     */
    virtual void drawWaveform(juce::Graphics& g, juce::Rectangle<int> bounds);
    
    /**
     * Draw the modulation source controls.
     * 
     * @param g The graphics context
     * @param bounds The bounds to draw in
     */
    virtual void drawControls(juce::Graphics& g, juce::Rectangle<int> bounds);
    
    /**
     * Draw the modulation source header.
     * 
     * @param g The graphics context
     * @param bounds The bounds to draw in
     */
    virtual void drawHeader(juce::Graphics& g, juce::Rectangle<int> bounds);
    
    /**
     * Timer callback for animation.
     */
    void timerCallback() override;
    
    ModulationSource* source;
    bool active = true;
    
private:
    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(ModulationSourceComponent)
};
