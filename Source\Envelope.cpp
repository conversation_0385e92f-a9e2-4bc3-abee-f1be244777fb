/*
  ==============================================================================

    Envelope.cpp
    Created: 21 May 2023
    Author:  AuraBloom Team

    Implementation of the Envelope class.

  ==============================================================================
*/

#include "Envelope.h"

Envelope::Envelope(const juce::String& name)
    : ModulationSource(name)
{
    // Envelopes are unipolar by default (0.0 to 1.0)
    setBipolar(false);
}

Envelope::~Envelope()
{
}

void Envelope::reset()
{
    ModulationSource::reset();
    currentStage = Idle;
    stageProgress = 0.0f;
    envelopeValue = 0.0f;
    releaseStartValue = 0.0f;
}

void Envelope::update(float deltaTime)
{
    if (!active || currentStage == Idle)
        return;
    
    // Update stage progress
    float stageDuration = 0.0f;
    
    switch (currentStage)
    {
        case Attack:
            stageDuration = attackTime;
            break;
            
        case Decay:
            stageDuration = decayTime;
            break;
            
        case Sustain:
            // Sustain stage has no duration
            stageDuration = 0.0f;
            break;
            
        case Release:
            stageDuration = releaseTime;
            break;
            
        default:
            stageDuration = 0.0f;
            break;
    }
    
    if (stageDuration > 0.0f)
    {
        stageProgress += deltaTime / stageDuration;
        
        // Check if stage is complete
        if (stageProgress >= 1.0f)
        {
            stageProgress = 0.0f;
            advanceToNextStage();
        }
    }
    
    // Calculate envelope value
    envelopeValue = calculateStageValue(currentStage, stageProgress);
    
    // Set the modulation value
    setValue(envelopeValue);
}

float Envelope::getValueAt(float timeOffset) const
{
    if (!active || currentStage == Idle)
        return 0.0f;
    
    // Calculate stage and progress at the specified time offset
    Stage offsetStage = currentStage;
    float offsetProgress = stageProgress;
    
    // Calculate stage duration
    float stageDuration = 0.0f;
    
    switch (offsetStage)
    {
        case Attack:
            stageDuration = attackTime;
            break;
            
        case Decay:
            stageDuration = decayTime;
            break;
            
        case Sustain:
            // Sustain stage has no duration
            return calculateStageValue(Sustain, 0.0f) * depth;
            
        case Release:
            stageDuration = releaseTime;
            break;
            
        default:
            return 0.0f;
    }
    
    // Update progress
    if (stageDuration > 0.0f)
    {
        offsetProgress += timeOffset / stageDuration;
        
        // Check if stage is complete
        while (offsetProgress >= 1.0f)
        {
            offsetProgress -= 1.0f;
            
            // Advance to next stage
            switch (offsetStage)
            {
                case Attack:
                    offsetStage = Decay;
                    stageDuration = decayTime;
                    break;
                    
                case Decay:
                    offsetStage = Sustain;
                    return calculateStageValue(Sustain, 0.0f) * depth;
                    
                case Release:
                    offsetStage = Idle;
                    return 0.0f;
                    
                default:
                    return 0.0f;
            }
        }
    }
    
    // Calculate envelope value
    return calculateStageValue(offsetStage, offsetProgress) * depth;
}

void Envelope::setAttackTime(float timeSeconds)
{
    attackTime = juce::jmax(0.001f, timeSeconds);
}

float Envelope::getAttackTime() const
{
    return attackTime;
}

void Envelope::setDecayTime(float timeSeconds)
{
    decayTime = juce::jmax(0.001f, timeSeconds);
}

float Envelope::getDecayTime() const
{
    return decayTime;
}

void Envelope::setSustainLevel(float level)
{
    sustainLevel = juce::jlimit(0.0f, 1.0f, level);
}

float Envelope::getSustainLevel() const
{
    return sustainLevel;
}

void Envelope::setReleaseTime(float timeSeconds)
{
    releaseTime = juce::jmax(0.001f, timeSeconds);
}

float Envelope::getReleaseTime() const
{
    return releaseTime;
}

void Envelope::setTriggerMode(TriggerMode mode)
{
    triggerMode = mode;
}

Envelope::TriggerMode Envelope::getTriggerMode() const
{
    return triggerMode;
}

void Envelope::setCurveType(Stage stage, CurveType type)
{
    switch (stage)
    {
        case Attack:
            attackCurve = type;
            break;
            
        case Decay:
            decayCurve = type;
            break;
            
        case Release:
            releaseCurve = type;
            break;
            
        default:
            break;
    }
}

Envelope::CurveType Envelope::getCurveType(Stage stage) const
{
    switch (stage)
    {
        case Attack:
            return attackCurve;
            
        case Decay:
            return decayCurve;
            
        case Release:
            return releaseCurve;
            
        default:
            return Linear;
    }
}

void Envelope::noteOn()
{
    if (currentStage == Idle || currentStage == Release || triggerMode == Trigger || triggerMode == OneShot)
    {
        // Start from the beginning
        currentStage = Attack;
        stageProgress = 0.0f;
    }
}

void Envelope::noteOff()
{
    if (triggerMode == Gate && (currentStage == Attack || currentStage == Decay || currentStage == Sustain))
    {
        // Move to release stage
        releaseStartValue = envelopeValue;
        currentStage = Release;
        stageProgress = 0.0f;
    }
}

Envelope::Stage Envelope::getCurrentStage() const
{
    return currentStage;
}

bool Envelope::isEnvelopeActive() const
{
    return currentStage != Idle;
}

float Envelope::calculateStageValue(Stage stage, float progress) const
{
    switch (stage)
    {
        case Attack:
        {
            // Attack: 0.0 to 1.0
            float curvedProgress = applyCurve(progress, attackCurve);
            return curvedProgress;
        }
            
        case Decay:
        {
            // Decay: 1.0 to sustainLevel
            float curvedProgress = applyCurve(progress, decayCurve);
            return 1.0f - curvedProgress * (1.0f - sustainLevel);
        }
            
        case Sustain:
            // Sustain: constant sustainLevel
            return sustainLevel;
            
        case Release:
        {
            // Release: releaseStartValue to 0.0
            float curvedProgress = applyCurve(progress, releaseCurve);
            return releaseStartValue * (1.0f - curvedProgress);
        }
            
        default:
            return 0.0f;
    }
}

float Envelope::applyCurve(float progress, CurveType curveType) const
{
    switch (curveType)
    {
        case Linear:
            return progress;
            
        case Exponential:
            // Exponential curve (slower at the beginning, faster at the end)
            return 1.0f - std::exp(-progress * 5.0f);
            
        case Logarithmic:
            // Logarithmic curve (faster at the beginning, slower at the end)
            return std::log(1.0f + progress * 9.0f) / std::log(10.0f);
            
        case SCurve:
            // S-curve (sigmoid)
            return 0.5f * (1.0f + std::sin((progress - 0.5f) * juce::MathConstants<float>::pi));
            
        default:
            return progress;
    }
}

void Envelope::advanceToNextStage()
{
    switch (currentStage)
    {
        case Attack:
            currentStage = Decay;
            break;
            
        case Decay:
            currentStage = Sustain;
            
            // If in OneShot or Loop mode, continue to Release after Sustain
            if (triggerMode == OneShot || triggerMode == Loop)
            {
                releaseStartValue = sustainLevel;
                currentStage = Release;
            }
            break;
            
        case Sustain:
            // Sustain stage doesn't advance automatically
            break;
            
        case Release:
            if (triggerMode == Loop)
            {
                // Loop back to Attack
                currentStage = Attack;
            }
            else
            {
                // End of envelope
                currentStage = Idle;
                envelopeValue = 0.0f;
            }
            break;
            
        default:
            currentStage = Idle;
            break;
    }
}
