/*
  ==============================================================================

    ModulationTarget.h
    Created: 21 May 2023
    Author:  AuraBloom Team

    This file defines the ModulationTarget base class, which represents a target
    that can be modulated by modulation sources.

  ==============================================================================
*/

#pragma once

#include <JuceHeader.h>

/**
 * Base class for all modulation targets.
 * 
 * A modulation target is a parameter or control that can be modulated
 * by modulation sources.
 */
class ModulationTarget
{
public:
    /**
     * Constructor.
     * 
     * @param name The name of the modulation target
     */
    ModulationTarget(const juce::String& name);
    
    /**
     * Destructor.
     */
    virtual ~ModulationTarget();
    
    /**
     * Get the name of the modulation target.
     * 
     * @return The name of the modulation target
     */
    const juce::String& getName() const;
    
    /**
     * Set the name of the modulation target.
     * 
     * @param name The new name of the modulation target
     */
    void setName(const juce::String& name);
    
    /**
     * Get the current value of the modulation target.
     * 
     * @return The current value of the modulation target
     */
    float getValue() const;
    
    /**
     * Set the base value of the modulation target.
     * 
     * @param value The base value of the modulation target
     */
    void setBaseValue(float value);
    
    /**
     * Get the base value of the modulation target.
     * 
     * @return The base value of the modulation target
     */
    float getBaseValue() const;
    
    /**
     * Set the modulation amount.
     * 
     * @param amount The modulation amount (-1.0 to 1.0)
     */
    void setModulationAmount(float amount);
    
    /**
     * Get the modulation amount.
     * 
     * @return The modulation amount
     */
    float getModulationAmount() const;
    
    /**
     * Set the modulation value.
     * 
     * @param value The modulation value (-1.0 to 1.0)
     */
    void setModulationValue(float value);
    
    /**
     * Get the modulation value.
     * 
     * @return The modulation value
     */
    float getModulationValue() const;
    
    /**
     * Set the minimum value of the target.
     * 
     * @param minValue The minimum value
     */
    void setMinValue(float minValue);
    
    /**
     * Get the minimum value of the target.
     * 
     * @return The minimum value
     */
    float getMinValue() const;
    
    /**
     * Set the maximum value of the target.
     * 
     * @param maxValue The maximum value
     */
    void setMaxValue(float maxValue);
    
    /**
     * Get the maximum value of the target.
     * 
     * @return The maximum value
     */
    float getMaxValue() const;
    
    /**
     * Set whether the target is bipolar.
     * 
     * @param bipolar True if the target is bipolar (-1.0 to 1.0), false if unipolar (0.0 to 1.0)
     */
    void setBipolar(bool bipolar);
    
    /**
     * Get whether the target is bipolar.
     * 
     * @return True if the target is bipolar
     */
    bool isBipolar() const;
    
    /**
     * Update the target value based on the base value and modulation.
     */
    virtual void update();
    
    /**
     * Apply the current value to the actual parameter or control.
     */
    virtual void applyValue() = 0;
    
protected:
    juce::String name;
    float baseValue = 0.0f;
    float modulationAmount = 0.0f;
    float modulationValue = 0.0f;
    float currentValue = 0.0f;
    float minValue = 0.0f;
    float maxValue = 1.0f;
    bool bipolar = false;
    
private:
    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(ModulationTarget)
};
