/*
  ==============================================================================

    MacroControlKnob.h
    Created: 21 May 2023
    Author:  AuraBloom Team

    宏控制旋钮 - 高级参数组合控制
    每个宏控制可以同时影响多个底层参数

  ==============================================================================
*/

#pragma once

#include <JuceHeader.h>

class MacroControlKnob : public juce::Component
{
public:
    // 宏控制类型
    enum MacroType
    {
        Genesis = 0,    // 创世 - 整体生命力和密度
        Scatter,        // 散射 - 空间和时间分布
        Form,           // 形态 - 颗粒形状和纹理
        Chaos           // 混沌 - 随机性和不可预测性
    };

    // 参数映射结构
    struct ParameterMapping
    {
        juce::String parameterId;
        float minValue;
        float maxValue;
        float curve;        // 映射曲线 (1.0 = 线性, >1.0 = 指数, <1.0 = 对数)
        bool inverted;      // 是否反向映射

        ParameterMapping(const juce::String& id, float min, float max,
                        float c = 1.0f, bool inv = false)
            : parameterId(id), minValue(min), maxValue(max),
              curve(c), inverted(inv) {}
    };

    MacroControlKnob(MacroType type, const juce::String& name);
    ~MacroControlKnob() override;

    // Component overrides
    void paint(juce::Graphics& g) override;
    void resized() override;
    void mouseDown(const juce::MouseEvent& e) override;
    void mouseDrag(const juce::MouseEvent& e) override;
    void mouseUp(const juce::MouseEvent& e) override;
    void mouseWheelMove(const juce::MouseEvent& e, const juce::MouseWheelDetails& wheel) override;

    // 值控制
    void setValue(float newValue, bool sendNotification = true);
    float getValue() const { return currentValue; }
    void setRange(float minValue, float maxValue);

    // 参数映射管理
    void addParameterMapping(const juce::String& parameterId, float minValue, float maxValue,
                           float curve = 1.0f, bool inverted = false);
    void clearParameterMappings();

    // 视觉设置
    void setKnobSize(float size) { knobSize = size; repaint(); }
    void setShowValue(bool show) { showValue = show; repaint(); }
    void setShowName(bool show) { showName = show; repaint(); }

    // 回调函数
    std::function<void(const juce::String&, float)> onParameterChange;
    std::function<void(float)> onValueChange;

private:
    // 基本属性
    MacroType macroType;
    juce::String macroName;
    float currentValue = 0.5f;
    float minValue = 0.0f;
    float maxValue = 1.0f;

    // 交互状态
    bool isDragging = false;
    float dragStartValue = 0.0f;
    juce::Point<float> dragStartPosition;

    // 视觉设置
    float knobSize = 60.0f;
    bool showValue = true;
    bool showName = true;

    // 参数映射
    std::vector<ParameterMapping> parameterMappings;

    // 内部方法
    void updateMappedParameters();
    float applyParameterCurve(float normalizedValue, float curve) const;
    juce::Colour getMacroColor() const;
    juce::String getMacroDescription() const;

    // 绘制方法
    void drawKnobBackground(juce::Graphics& g, juce::Rectangle<float> knobArea);
    void drawKnobIndicator(juce::Graphics& g, juce::Rectangle<float> knobArea);
    void drawValueText(juce::Graphics& g, juce::Rectangle<float> area);
    void drawNameText(juce::Graphics& g, juce::Rectangle<float> area);
    void drawDescriptionText(juce::Graphics& g, juce::Rectangle<float> area);
    void drawGlow(juce::Graphics& g, juce::Rectangle<float> knobArea);

    // 默认参数映射设置
    void setupDefaultMappings();
    void setupGenesisMappings();
    void setupScatterMappings();
    void setupFormMappings();
    void setupChaosMappings();

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(MacroControlKnob)
};
