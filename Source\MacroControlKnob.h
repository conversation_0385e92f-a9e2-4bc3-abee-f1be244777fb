/*
  ==============================================================================

    MacroControlKnob.h
    Created: 21 May 2023
    Author:  AuraBloom Team

    宏控制旋钮 - 高级参数组合控制
    每个宏控制可以同时影响多个底层参数

  ==============================================================================
*/

#pragma once

#include <JuceHeader.h>

class MacroControlKnob : public juce::Component
{
public:
    // Macro control types
    enum MacroType
    {
        Genesis = 0,    // Genesis - Overall vitality and density
        Scatter,        // Scatter - Spatial and temporal distribution
        Form,           // Form - Particle shape and texture
        Chaos           // Chaos - Randomness and unpredictability
    };

    // 参数映射结构
    struct ParameterMapping
    {
        juce::String parameterId;
        float minValue;
        float maxValue;
        float curve;        // Mapping curve (1.0 = linear, >1.0 = exponential, <1.0 = logarithmic)
        bool inverted;      // Whether to invert mapping

        ParameterMapping(const juce::String& id, float min, float max,
                        float c = 1.0f, bool inv = false)
            : parameterId(id), minValue(min), maxValue(max),
              curve(c), inverted(inv) {}
    };

    MacroControlKnob(MacroType type, const juce::String& name);
    ~MacroControlKnob() override;

    // Component overrides
    void paint(juce::Graphics& g) override;
    void resized() override;
    void mouseDown(const juce::MouseEvent& e) override;
    void mouseDrag(const juce::MouseEvent& e) override;
    void mouseUp(const juce::MouseEvent& e) override;
    void mouseWheelMove(const juce::MouseEvent& e, const juce::MouseWheelDetails& wheel) override;

    // Value control
    void setValue(float newValue, bool sendNotification = true);
    float getValue() const { return currentValue; }
    void setRange(float minValue, float maxValue);

    // Parameter mapping management
    void addParameterMapping(const juce::String& parameterId, float minValue, float maxValue,
                           float curve = 1.0f, bool inverted = false);
    void clearParameterMappings();

    // Visual settings
    void setKnobSize(float size) { knobSize = size; repaint(); }
    void setShowValue(bool show) { showValue = show; repaint(); }
    void setShowName(bool show) { showName = show; repaint(); }

    // Callback functions
    std::function<void(const juce::String&, float)> onParameterChange;
    std::function<void(float)> onValueChange;

private:
    // Basic properties
    MacroType macroType;
    juce::String macroName;
    float currentValue = 0.5f;
    float minValue = 0.0f;
    float maxValue = 1.0f;

    // Interaction state
    bool isDragging = false;
    float dragStartValue = 0.0f;
    juce::Point<float> dragStartPosition;

    // Visual settings
    float knobSize = 60.0f;
    bool showValue = true;
    bool showName = true;

    // Parameter mappings
    std::vector<ParameterMapping> parameterMappings;

    // Internal methods
    void updateMappedParameters();
    float applyParameterCurve(float normalizedValue, float curve) const;
    juce::Colour getMacroColor() const;
    juce::String getMacroDescription() const;

    // Drawing methods
    void drawKnobBackground(juce::Graphics& g, juce::Rectangle<float> knobArea);
    void drawKnobIndicator(juce::Graphics& g, juce::Rectangle<float> knobArea);
    void drawValueText(juce::Graphics& g, juce::Rectangle<float> area);
    void drawNameText(juce::Graphics& g, juce::Rectangle<float> area);
    void drawDescriptionText(juce::Graphics& g, juce::Rectangle<float> area);
    void drawGlow(juce::Graphics& g, juce::Rectangle<float> knobArea);

    // Default parameter mapping setup
    void setupDefaultMappings();
    void setupGenesisMappings();
    void setupScatterMappings();
    void setupFormMappings();
    void setupChaosMappings();

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(MacroControlKnob)
};
