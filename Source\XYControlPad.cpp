/*
  ==============================================================================

    XYControlPad.cpp
    Created: 21 May 2023
    Author:  AuraBloom Team

    XY控制板的实现 - 多参数同时控制

  ==============================================================================
*/

#include "XYControlPad.h"

XYControlPad::XYControlPad()
{
    setInterceptsMouseClicks(true, true);

    // 设置默认的参数映射
    addXAxisParameter("rate", 0.1f, 10.0f);      // X轴: 颗粒速率
    addXAxisParameter("grainSize", 10.0f, 200.0f); // X轴: 颗粒大小
    addXAxisParameter("pitch", -12.0f, 12.0f);    // X轴: 音高偏移

    addYAxisParameter("density", 0.1f, 1.0f);     // Y轴: 颗粒密度
    addYAxisParameter("texture", 0.0f, 1.0f);     // Y轴: 纹理强度
    addYAxisParameter("spread", 0.0f, 1.0f);      // Y轴: 立体声展开

    // 初始化参数值
    updateParametersFromPosition();
}

XYControlPad::~XYControlPad()
{
}

void XYControlPad::paint(juce::Graphics& g)
{
    drawBackground(g);

    if (showGrid)
        drawGrid(g);

    drawParameterLabels(g);

    if (showTrail)
        drawTrail(g);

    drawControlPoint(g);
    drawParameterValues(g);
}

void XYControlPad::drawBackground(juce::Graphics& g)
{
    auto bounds = getLocalBounds().toFloat();

    // 创建深色渐变背景
    juce::ColourGradient gradient(
        juce::Colour(25, 25, 35),    // 深灰蓝
        bounds.getCentreX(), bounds.getY(),
        juce::Colour(35, 25, 45),    // 深紫
        bounds.getCentreX(), bounds.getBottom(),
        false
    );

    g.setGradientFill(gradient);
    g.fillRoundedRectangle(bounds, 8.0f);

    // 边框
    g.setColour(juce::Colour(80, 80, 100));
    g.drawRoundedRectangle(bounds, 8.0f, 2.0f);
}

void XYControlPad::drawGrid(juce::Graphics& g)
{
    auto bounds = getLocalBounds().toFloat().reduced(10.0f);

    g.setColour(juce::Colours::white.withAlpha(0.1f));

    // 垂直网格线
    for (int i = 1; i < 4; ++i)
    {
        float x = bounds.getX() + bounds.getWidth() * i / 4.0f;
        g.drawVerticalLine(static_cast<int>(x), bounds.getY(), bounds.getBottom());
    }

    // 水平网格线
    for (int i = 1; i < 4; ++i)
    {
        float y = bounds.getY() + bounds.getHeight() * i / 4.0f;
        g.drawHorizontalLine(static_cast<int>(y), bounds.getX(), bounds.getRight());
    }

    // 中心十字线
    g.setColour(juce::Colours::white.withAlpha(0.2f));
    float centerX = bounds.getCentreX();
    float centerY = bounds.getCentreY();
    g.drawVerticalLine(static_cast<int>(centerX), bounds.getY(), bounds.getBottom());
    g.drawHorizontalLine(static_cast<int>(centerY), bounds.getX(), bounds.getRight());
}

void XYControlPad::drawParameterLabels(juce::Graphics& g)
{
    auto bounds = getLocalBounds().toFloat();

    g.setColour(juce::Colours::white.withAlpha(0.7f));
    g.setFont(11.0f);

    // X轴标签 (底部)
    juce::String xLabels = "X轴: ";
    for (size_t i = 0; i < xAxisMappings.size(); ++i)
    {
        if (i > 0) xLabels += ", ";
        xLabels += xAxisMappings[i].parameterId;
    }
    g.drawText(xLabels, bounds.removeFromBottom(20).reduced(5, 0),
               juce::Justification::centredLeft);

    // Y轴标签 (左侧，旋转90度)
    juce::String yLabels = "Y轴: ";
    for (size_t i = 0; i < yAxisMappings.size(); ++i)
    {
        if (i > 0) yLabels += ", ";
        yLabels += yAxisMappings[i].parameterId;
    }

    g.saveState();
    g.addTransform(juce::AffineTransform::rotation(-juce::MathConstants<float>::halfPi,
                                                   bounds.getX() + 10, bounds.getCentreY()));
    g.drawText(yLabels, 0, 0, 100, 20, juce::Justification::centredLeft);
    g.restoreState();
}

void XYControlPad::drawTrail(juce::Graphics& g)
{
    if (trailPoints.size() < 2) return;

    juce::Path trailPath;
    bool firstPoint = true;

    for (size_t i = 0; i < trailPoints.size(); ++i)
    {
        auto screenPoint = normalizedToScreen(trailPoints[i]);

        if (firstPoint)
        {
            trailPath.startNewSubPath(screenPoint);
            firstPoint = false;
        }
        else
        {
            trailPath.lineTo(screenPoint);
        }
    }

    // 渐变透明度的轨迹
    for (size_t i = 1; i < trailPoints.size(); ++i)
    {
        float alpha = static_cast<float>(i) / static_cast<float>(trailPoints.size()) * 0.5f;
        g.setColour(juce::Colour::fromRGBA(100, 200, 255, static_cast<juce::uint8>(alpha * 255)));

        auto p1 = normalizedToScreen(trailPoints[i-1]);
        auto p2 = normalizedToScreen(trailPoints[i]);
        g.drawLine(p1.x, p1.y, p2.x, p2.y, 2.0f);
    }
}

void XYControlPad::drawControlPoint(juce::Graphics& g)
{
    auto screenPos = normalizedToScreen(currentPosition);

    // 外圈光晕
    g.setColour(juce::Colour::fromRGBA(100, 200, 255, 76));
    g.fillEllipse(screenPos.x - controlPointSize, screenPos.y - controlPointSize,
                  controlPointSize * 2, controlPointSize * 2);

    // 主控制点
    g.setColour(isDragging ? juce::Colour(150, 220, 255) : juce::Colour(100, 200, 255));
    g.fillEllipse(screenPos.x - controlPointSize * 0.6f, screenPos.y - controlPointSize * 0.6f,
                  controlPointSize * 1.2f, controlPointSize * 1.2f);

    // 中心点
    g.setColour(juce::Colours::white);
    g.fillEllipse(screenPos.x - 2, screenPos.y - 2, 4, 4);
}

void XYControlPad::drawParameterValues(juce::Graphics& g)
{
    auto bounds = getLocalBounds().toFloat();

    g.setColour(juce::Colours::white.withAlpha(0.8f));
    g.setFont(10.0f);

    // 显示当前坐标
    juce::String coordText = juce::String::formatted("X: %.2f, Y: %.2f",
                                                     currentPosition.x, currentPosition.y);
    g.drawText(coordText, bounds.getX() + 5, bounds.getY() + 5, 100, 15,
               juce::Justification::topLeft);
}

void XYControlPad::resized()
{
    // 组件大小改变时无需特殊处理
}

void XYControlPad::mouseDown(const juce::MouseEvent& e)
{
    isDragging = true;
    currentPosition = screenToNormalized(e.position);

    // 清空轨迹并添加起始点
    trailPoints.clear();
    trailPoints.push_back(currentPosition);

    updateParametersFromPosition();
    repaint();
}

void XYControlPad::mouseDrag(const juce::MouseEvent& e)
{
    if (isDragging)
    {
        currentPosition = screenToNormalized(e.position);

        // 添加轨迹点
        trailPoints.push_back(currentPosition);
        if (trailPoints.size() > maxTrailPoints)
        {
            trailPoints.erase(trailPoints.begin());
        }

        updateParametersFromPosition();

        if (onPositionChange)
            onPositionChange(currentPosition.x, currentPosition.y);

        repaint();
    }
}

void XYControlPad::mouseUp(const juce::MouseEvent& e)
{
    isDragging = false;
    repaint();
}

// 参数映射管理
void XYControlPad::addXAxisParameter(const juce::String& parameterId, float minValue, float maxValue)
{
    xAxisMappings.emplace_back(parameterId, minValue, maxValue);
}

void XYControlPad::addYAxisParameter(const juce::String& parameterId, float minValue, float maxValue)
{
    yAxisMappings.emplace_back(parameterId, minValue, maxValue);
}

void XYControlPad::removeParameter(const juce::String& parameterId)
{
    // 从X轴映射中移除
    xAxisMappings.erase(
        std::remove_if(xAxisMappings.begin(), xAxisMappings.end(),
                      [&parameterId](const ParameterMapping& mapping) {
                          return mapping.parameterId == parameterId;
                      }),
        xAxisMappings.end());

    // 从Y轴映射中移除
    yAxisMappings.erase(
        std::remove_if(yAxisMappings.begin(), yAxisMappings.end(),
                      [&parameterId](const ParameterMapping& mapping) {
                          return mapping.parameterId == parameterId;
                      }),
        yAxisMappings.end());
}

void XYControlPad::clearAllMappings()
{
    xAxisMappings.clear();
    yAxisMappings.clear();
}

void XYControlPad::setXYPosition(float x, float y)
{
    currentPosition.x = juce::jlimit(0.0f, 1.0f, x);
    currentPosition.y = juce::jlimit(0.0f, 1.0f, y);
    updateParametersFromPosition();
    repaint();
}

float XYControlPad::getParameterValue(const juce::String& parameterId) const
{
    // 在X轴映射中查找
    for (const auto& mapping : xAxisMappings)
    {
        if (mapping.parameterId == parameterId)
            return mapping.currentValue;
    }

    // 在Y轴映射中查找
    for (const auto& mapping : yAxisMappings)
    {
        if (mapping.parameterId == parameterId)
            return mapping.currentValue;
    }

    return 0.0f; // 未找到参数
}

// 内部方法
void XYControlPad::updateParametersFromPosition()
{
    // 更新X轴参数
    updateParameterMapping(xAxisMappings, currentPosition.x);

    // 更新Y轴参数
    updateParameterMapping(yAxisMappings, currentPosition.y);
}

void XYControlPad::updateParameterMapping(std::vector<ParameterMapping>& mappings, float normalizedValue)
{
    for (auto& mapping : mappings)
    {
        if (mapping.isActive)
        {
            mapping.currentValue = mapping.minValue +
                                 (mapping.maxValue - mapping.minValue) * normalizedValue;

            if (onParameterChange)
                onParameterChange(mapping.parameterId, mapping.currentValue);
        }
    }
}

juce::Point<float> XYControlPad::screenToNormalized(juce::Point<float> screenPoint) const
{
    auto bounds = getLocalBounds().toFloat().reduced(10.0f);

    float x = juce::jlimit(0.0f, 1.0f, (screenPoint.x - bounds.getX()) / bounds.getWidth());
    float y = juce::jlimit(0.0f, 1.0f, 1.0f - (screenPoint.y - bounds.getY()) / bounds.getHeight());

    return {x, y};
}

juce::Point<float> XYControlPad::normalizedToScreen(juce::Point<float> normalizedPoint) const
{
    auto bounds = getLocalBounds().toFloat().reduced(10.0f);

    float x = bounds.getX() + normalizedPoint.x * bounds.getWidth();
    float y = bounds.getY() + (1.0f - normalizedPoint.y) * bounds.getHeight();

    return {x, y};
}
