/*
  ==============================================================================

    LFO.cpp
    Created: 21 May 2023
    Author:  AuraBloom Team

    Implementation of the LFO class.

  ==============================================================================
*/

#include "LFO.h"

LFO::LFO(const juce::String& name)
    : ModulationSource(name)
{
    // Initialize with a default sine wave
    customWaveform.resize(1024, 0.0f);
    for (int i = 0; i < 1024; ++i)
    {
        float phase = static_cast<float>(i) / 1024.0f;
        customWaveform[i] = std::sin(phase * juce::MathConstants<float>::twoPi);
    }
}

LFO::~LFO()
{
}

void LFO::reset()
{
    ModulationSource::reset();
    triggered = false;
}

void LFO::update(float deltaTime)
{
    if (!active)
        return;
    
    // Update phase
    float effectiveFrequency = calculateEffectiveFrequency();
    phase += deltaTime * effectiveFrequency;
    
    // Wrap phase to 0.0-1.0
    phase = phase - std::floor(phase);
    
    // Calculate value based on waveform type and phase
    float rawValue = calculateWaveformValue(waveformType, phase);
    
    // Apply depth and set value
    setValue(rawValue);
}

float LFO::getValueAt(float timeOffset) const
{
    if (!active)
        return 0.0f;
    
    // Calculate phase at the specified time offset
    float effectiveFrequency = calculateEffectiveFrequency();
    float offsetPhase = phase + timeOffset * effectiveFrequency;
    
    // Wrap phase to 0.0-1.0
    offsetPhase = offsetPhase - std::floor(offsetPhase);
    
    // Calculate value based on waveform type and phase
    float rawValue = calculateWaveformValue(waveformType, offsetPhase);
    
    // Apply depth
    return rawValue * depth;
}

void LFO::setWaveformType(WaveformType type)
{
    waveformType = type;
}

LFO::WaveformType LFO::getWaveformType() const
{
    return waveformType;
}

void LFO::setFrequency(float frequencyHz)
{
    frequency = juce::jmax(0.01f, frequencyHz);
}

float LFO::getFrequency() const
{
    return frequency;
}

void LFO::setSyncMode(SyncMode mode)
{
    syncMode = mode;
}

LFO::SyncMode LFO::getSyncMode() const
{
    return syncMode;
}

void LFO::setTempoSyncNote(float noteValue)
{
    tempoSyncNote = juce::jmax(0.01f, noteValue);
}

float LFO::getTempoSyncNote() const
{
    return tempoSyncNote;
}

void LFO::setHostBPM(double bpm)
{
    hostBPM = juce::jmax(1.0, bpm);
}

double LFO::getHostBPM() const
{
    return hostBPM;
}

void LFO::setCustomWaveform(const float* waveform, int size)
{
    if (size <= 0 || waveform == nullptr)
        return;
    
    customWaveform.resize(size);
    std::memcpy(customWaveform.data(), waveform, size * sizeof(float));
}

void LFO::trigger()
{
    triggered = true;
    phase = retriggerPhase;
}

void LFO::setRetriggerPhase(float phase)
{
    retriggerPhase = juce::jlimit(0.0f, 1.0f, phase);
}

float LFO::getRetriggerPhase() const
{
    return retriggerPhase;
}

float LFO::calculateWaveformValue(WaveformType type, float phase) const
{
    switch (type)
    {
        case Sine:
            return std::sin(phase * juce::MathConstants<float>::twoPi);
            
        case Triangle:
            return 1.0f - std::abs(2.0f * phase - 1.0f) * 2.0f;
            
        case Square:
            return (phase < 0.5f) ? 1.0f : -1.0f;
            
        case Sawtooth:
            return 2.0f * phase - 1.0f;
            
        case Reverse:
            return 1.0f - 2.0f * phase;
            
        case Random:
            // Use phase to seed random number generator for reproducible randomness
            {
                int seed = static_cast<int>(phase * 1000000.0f);
                juce::Random random(seed);
                return random.nextFloat() * 2.0f - 1.0f;
            }
            
        case Noise:
            // Smoothed noise using cubic interpolation between random points
            {
                float p0 = std::floor(phase * 4.0f) / 4.0f;
                float p1 = p0 + 0.25f;
                if (p1 >= 1.0f) p1 -= 1.0f;
                
                int seed0 = static_cast<int>(p0 * 1000000.0f);
                int seed1 = static_cast<int>(p1 * 1000000.0f);
                
                juce::Random random0(seed0);
                juce::Random random1(seed1);
                
                float v0 = random0.nextFloat() * 2.0f - 1.0f;
                float v1 = random1.nextFloat() * 2.0f - 1.0f;
                
                float t = (phase - p0) / 0.25f; // Normalized position between p0 and p1
                
                // Cubic interpolation
                float t2 = t * t;
                float t3 = t2 * t;
                return v0 * (1.0f - 3.0f * t2 + 2.0f * t3) + v1 * (3.0f * t2 - 2.0f * t3);
            }
            
        case Custom:
            // Interpolate in custom waveform
            if (customWaveform.empty())
                return 0.0f;
            
            {
                float index = phase * static_cast<float>(customWaveform.size());
                int index0 = static_cast<int>(index);
                int index1 = (index0 + 1) % customWaveform.size();
                float frac = index - static_cast<float>(index0);
                
                return customWaveform[index0] * (1.0f - frac) + customWaveform[index1] * frac;
            }
            
        default:
            return 0.0f;
    }
}

float LFO::calculateEffectiveFrequency() const
{
    switch (syncMode)
    {
        case Free:
            return frequency;
            
        case Tempo:
            // Convert BPM to Hz based on note value
            // Quarter note = 1.0, Eighth note = 0.5, etc.
            return static_cast<float>(hostBPM / 60.0 / tempoSyncNote);
            
        case Note:
            // In Note mode, the LFO is triggered by note events
            // and the frequency determines how many cycles per note
            return frequency;
            
        default:
            return frequency;
    }
}
