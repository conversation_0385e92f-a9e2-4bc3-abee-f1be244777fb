# AuraBloom Windows构建指南

## 🎯 概述
本指南将帮助您在Windows系统上编译AuraBloom VST3插件。

## 📋 系统要求

### 必需软件
1. **Visual Studio 2022** (Community版免费)
   - 下载地址: https://visualstudio.microsoft.com/downloads/
   - 必须安装"使用C++的桌面开发"工作负载

2. **JUCE Framework**
   - 下载地址: https://juce.com/get-juce
   - 建议安装到: `C:\JUCE\`

### 可选软件
- **Git for Windows** (如果需要版本控制)
- **VST3 SDK** (JUCE已包含，但可单独下载最新版)

## 🔧 构建步骤

### 步骤1: 准备项目文件
1. 将整个AuraBloom项目文件夹复制到Windows系统
2. 确保项目结构如下：
   ```
   AuraBloom/
   ├── Source/
   ├── Builds/
   │   └── VisualStudio2022/
   ├── JuceLibraryCode/
   └── AuraBloom.jucer
   ```

### 步骤2: 配置JUCE路径
1. 打开 `Builds/VisualStudio2022/AuraBloom.sln`
2. 如果出现路径错误，需要更新JUCE模块路径
3. 在项目属性中检查包含目录指向正确的JUCE安装位置

### 步骤3: 编译项目
1. 在Visual Studio中打开解决方案文件
2. 选择构建配置：
   - **Debug**: 用于开发和调试
   - **Release**: 用于最终发布
3. 选择目标平台：
   - **x64**: 64位Windows (推荐)
   - **Win32**: 32位Windows (如需要)
4. 右键点击 `AuraBloom_VST3` 项目
5. 选择"生成"或按 `Ctrl+Shift+B`

### 步骤4: 安装插件
编译成功后，VST3文件将位于：
```
Builds/VisualStudio2022/x64/Debug/VST3/AuraBloom.vst3
```
或
```
Builds/VisualStudio2022/x64/Release/VST3/AuraBloom.vst3
```

将 `.vst3` 文件夹复制到Windows VST3目录：
```
C:\Program Files\Common Files\VST3\
```

## 🚨 常见问题解决

### 问题1: JUCE模块未找到
**解决方案**: 
1. 检查JUCE安装路径
2. 在项目属性中更新包含目录
3. 确保JUCE_MODULE_AVAILABLE_* 宏定义正确

### 问题2: Windows SDK版本不匹配
**解决方案**:
1. 在项目属性中选择已安装的Windows SDK版本
2. 或安装项目要求的SDK版本

### 问题3: 编译错误
**解决方案**:
1. 确保所有源文件都在项目中
2. 检查预处理器定义
3. 验证链接器设置

## 📦 发布准备

### Debug版本
- 包含调试信息，文件较大
- 用于开发和测试

### Release版本
- 优化的最终版本
- 文件较小，性能更好
- 用于最终发布

## 🔍 验证安装
1. 在DAW中扫描VST3插件
2. 加载AuraBloom插件
3. 测试基本功能：
   - Mix滑块
   - Rate滑块
   - 音频处理

## 📞 技术支持
如果遇到编译问题，请检查：
1. Visual Studio版本兼容性
2. JUCE版本兼容性
3. Windows SDK版本
4. 项目配置设置
