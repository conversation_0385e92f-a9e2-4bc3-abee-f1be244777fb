/*
  ==============================================================================

    ParticleSystem.h
    Created: 21 May 2023
    Author:  AuraBloom Team

    This file defines the ParticleSystem class, which manages a collection of
    particles and their interactions.

  ==============================================================================
*/

#pragma once

#include <JuceHeader.h>
#include "Particle.h"
#include "PerformanceMonitor.h"
#include <array>
#include <memory>

/**
 * Forward declaration for force field
 */
class ForceField;

/**
 * Class representing a spatial grid cell for optimization
 */
struct GridCell
{
    std::vector<int> particleIndices;
};

/**
 * Class managing a collection of particles and their interactions
 */
class ParticleSystem
{
public:
    ParticleSystem();
    ~ParticleSystem();

    // Initialize the particle system
    void initialize(int maxParticles, double sampleRate, int samplesPerBlock);

    // Reset the particle system
    void reset();

    // Update all particles
    void update(float deltaTime);

    // Process audio
    void processAudio(juce::AudioBuffer<float>& buffer, const juce::AudioBuffer<float>& inputBuffer);

    // Emit a new particle
    void emitParticle(float x, float y, float vx, float vy);

    // Emit a new particle with specific properties
    void emitParticleWithProperties(float x, float y, float vx, float vy,
                                   ParticleType type, float mass, float charge,
                                   float energy, float lifespan);

    // Create a new particle and return a pointer to it
    Particle* createParticle();

    // Set behavior mode
    enum BehaviorMode
    {
        Swarm,          // Classic flocking behavior
        Repel,          // Particles repel each other
        Attract,        // Particles attract each other
        Orbit,          // Particles orbit around center
        Fluid,          // Fluid-like behavior
        Chaos,          // Chaotic behavior
        Crystallize,    // Particles form crystal-like structures
        Wave            // Wave-like patterns
    };

    void setBehaviorMode(BehaviorMode mode);

    // Set behavior intensity
    void setBehaviorIntensity(float intensity);

    // Set interaction radius
    void setInteractionRadius(float radius);

    // Set damping
    void setDamping(float damping);

    // Set boundaries
    void setBoundaries(float left, float right, float top, float bottom);

    // Set boundary behavior
    enum BoundaryBehavior
    {
        Wrap,       // Particles wrap around boundaries
        Bounce,     // Particles bounce off boundaries
        Absorb,     // Particles are absorbed at boundaries
        Reflect     // Particles are reflected at boundaries
    };

    void setBoundaryBehavior(BoundaryBehavior behavior);

    // Add a force field
    void addForceField(std::unique_ptr<ForceField> field);

    // Clear all force fields
    void clearForceFields();

    // Apply audio analysis to particles
    void applyAudioAnalysis(const juce::AudioBuffer<float>& buffer);

    // Get particle count
    int getParticleCount() const;

    // Get active particle count
    int getActiveParticleCount() const;

    // Get particles (for visualization)
    const std::vector<Particle>& getParticles() const;

    // Get system boundaries
    void getBoundaries(float& left, float& right, float& top, float& bottom) const;

    // Handle user interaction
    void handleUserInteraction(float x, float y, float strength, bool attract);

    // Clear all particles
    void clear();

    // Performance monitoring
    const PerformanceMonitor& getPerformanceMonitor() const { return performanceMonitor; }
    void setPerformanceTarget(float targetCpuUsage, float maxFrameTime);
    void enableAdaptivePerformance(bool enabled);

    // Enhanced collision system (Stage 5)
    void enableAdvancedCollisions(bool enabled);
    void setCollisionElasticity(float elasticity);
    void setCollisionAudioTriggers(bool enabled);

    // Memory management
    size_t getMemoryUsage() const;
    void optimizeMemory();

    // Particle count stabilization
    void stabilizeParticleCount(int currentActiveCount);

    // Enhanced collision handling (Stage 5)
    void handleAdvancedCollisions(float deltaTime);
    void processCollisionAudioEvent(Particle& p1, Particle& p2, float impactVelocity);

private:
    // Particle collection
    std::vector<Particle> particles;

    // Performance optimization components
    PerformanceMonitor performanceMonitor;

    // Enhanced collision parameters (Stage 5)
    bool advancedCollisionsEnabled = false;
    float collisionElasticity = 0.8f;
    bool collisionAudioTriggersEnabled = true;

    // Particle count stabilization
    float targetParticleCount = 50.0f;
    float particleCountSmoothingFactor = 0.1f;
    float lastActiveCount = 0.0f;

    // System parameters
    BehaviorMode behaviorMode = Swarm;
    float behaviorIntensity = 0.5f;
    float interactionRadius = 20.0f;
    float damping = 0.1f;

    // Boundary parameters
    float boundaryLeft = -100.0f;
    float boundaryRight = 100.0f;
    float boundaryTop = -100.0f;
    float boundaryBottom = 100.0f;
    BoundaryBehavior boundaryBehavior = Bounce;

    // Force fields
    std::vector<std::unique_ptr<ForceField>> forceFields;

    // Spatial partitioning
    static constexpr int GRID_SIZE = 10;
    std::array<std::array<GridCell, GRID_SIZE>, GRID_SIZE> grid;

    // Audio parameters
    double sampleRate = 44100.0;
    int samplesPerBlock = 512;

    // Audio analysis
    juce::dsp::FFT fft;
    std::vector<float> fftData;
    std::vector<float> fftMagnitudes;

    // Internal buffer for granular processing
    juce::AudioBuffer<float> grainBuffer;

    // Internal methods
    void applyBehavior(float deltaTime);
    void applySwarmBehavior(float deltaTime);
    void applyRepelBehavior(float deltaTime);
    void applyAttractBehavior(float deltaTime);
    void applyOrbitBehavior(float deltaTime);
    void applyFluidBehavior(float deltaTime);
    void applyChaosBehavior(float deltaTime);
    void applyCrystallizeBehavior(float deltaTime);
    void applyWaveBehavior(float deltaTime);

    // Apply force fields
    void applyForceFields(float deltaTime);

    // Handle particle collisions
    void handleCollisions();

    // Handle boundary interactions
    void handleBoundaries();

    // Update spatial partitioning grid
    void updateGrid();

    // Get grid cell for position
    void getGridCell(float x, float y, int& gridX, int& gridY);

    // Find an inactive particle
    int findInactiveParticle();

    // Process a single grain
    void processGrain(Particle& particle, juce::AudioBuffer<float>& outputBuffer, const juce::AudioBuffer<float>& inputBuffer);

    // Apply grain envelope
    float applyEnvelope(const Particle& particle, float normalizedPosition);

    // Perform FFT analysis
    void performFFTAnalysis(const juce::AudioBuffer<float>& buffer);
};
