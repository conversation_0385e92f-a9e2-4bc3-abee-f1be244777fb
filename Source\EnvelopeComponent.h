/*
  ==============================================================================

    EnvelopeComponent.h
    Created: 21 May 2023
    Author:  AuraBloom Team

    This file defines the EnvelopeComponent class, which is used to display and
    control an Envelope.

  ==============================================================================
*/

#pragma once

#include <JuceHeader.h>
#include "ModulationSourceComponent.h"
#include "Envelope.h"

/**
 * Component for displaying and controlling an Envelope.
 */
class EnvelopeComponent : public ModulationSourceComponent
{
public:
    /**
     * Constructor.
     * 
     * @param envelope The Envelope to display and control
     */
    EnvelopeComponent(Envelope* envelope);
    
    /**
     * Destructor.
     */
    ~EnvelopeComponent() override;
    
    /**
     * Handle component resize.
     */
    void resized() override;
    
protected:
    /**
     * Draw the envelope waveform.
     * 
     * @param g The graphics context
     * @param bounds The bounds to draw in
     */
    void drawWaveform(juce::Graphics& g, juce::Rectangle<int> bounds) override;
    
    /**
     * Draw the envelope controls.
     * 
     * @param g The graphics context
     * @param bounds The bounds to draw in
     */
    void drawControls(juce::Graphics& g, juce::Rectangle<int> bounds) override;
    
private:
    // Get the Envelope
    Envelope* getEnvelope() const;
    
    // UI Components
    juce::Slider attackSlider;
    juce::Slider decaySlider;
    juce::Slider sustainSlider;
    juce::Slider releaseSlider;
    juce::ComboBox triggerModeComboBox;
    juce::TextButton triggerButton;
    juce::TextButton releaseButton;
    
    // Labels
    juce::Label attackLabel;
    juce::Label decayLabel;
    juce::Label sustainLabel;
    juce::Label releaseLabel;
    juce::Label triggerModeLabel;
    
    // Helper methods
    void setupSlider(juce::Slider& slider, juce::Label& label, const juce::String& labelText);
    void setupComboBox(juce::ComboBox& comboBox, juce::Label& label, const juce::String& labelText);
    void setupButton(juce::TextButton& button, const juce::String& buttonText);
    
    // Listeners
    void attackChanged();
    void decayChanged();
    void sustainChanged();
    void releaseChanged();
    void triggerModeChanged();
    void triggerClicked();
    void releaseClicked();
    
    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(EnvelopeComponent)
};
