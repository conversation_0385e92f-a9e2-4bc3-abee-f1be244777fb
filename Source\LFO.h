/*
  ==============================================================================

    LFO.h
    Created: 21 May 2023
    Author:  AuraBloom Team

    This file defines the LFO class, which represents a low-frequency oscillator
    modulation source.

  ==============================================================================
*/

#pragma once

#include <JuceHeader.h>
#include "ModulationSource.h"

/**
 * Low-frequency oscillator modulation source.
 * 
 * An LFO generates a periodic waveform that can be used to modulate
 * parameters in the plugin.
 */
class LFO : public ModulationSource
{
public:
    /**
     * Waveform types for the LFO.
     */
    enum WaveformType
    {
        Sine,       // Sine wave
        Triangle,   // Triangle wave
        Square,     // Square wave
        Sawtooth,   // Sawtooth wave (rising)
        Reverse,    // Reverse sawtooth wave (falling)
        Random,     // Random values
        Noise,      // Smoothed noise
        Custom      // Custom waveform
    };
    
    /**
     * Sync modes for the LFO.
     */
    enum SyncMode
    {
        Free,       // Free-running (not synced)
        Tempo,      // Synced to host tempo
        Note        // Triggered by note events
    };
    
    /**
     * Constructor.
     * 
     * @param name The name of the LFO
     */
    LFO(const juce::String& name = "LFO");
    
    /**
     * Destructor.
     */
    ~LFO() override;
    
    /**
     * Reset the LFO.
     */
    void reset() override;
    
    /**
     * Update the LFO.
     * 
     * @param deltaTime Time elapsed since last update in seconds
     */
    void update(float deltaTime) override;
    
    /**
     * Get the LFO value at a specific time offset.
     * 
     * @param timeOffset Time offset in seconds
     * @return The LFO value at the specified time offset
     */
    float getValueAt(float timeOffset) const override;
    
    /**
     * Set the waveform type.
     * 
     * @param type The waveform type
     */
    void setWaveformType(WaveformType type);
    
    /**
     * Get the waveform type.
     * 
     * @return The waveform type
     */
    WaveformType getWaveformType() const;
    
    /**
     * Set the frequency in Hz.
     * 
     * @param frequencyHz The frequency in Hz
     */
    void setFrequency(float frequencyHz);
    
    /**
     * Get the frequency in Hz.
     * 
     * @return The frequency in Hz
     */
    float getFrequency() const;
    
    /**
     * Set the sync mode.
     * 
     * @param mode The sync mode
     */
    void setSyncMode(SyncMode mode);
    
    /**
     * Get the sync mode.
     * 
     * @return The sync mode
     */
    SyncMode getSyncMode() const;
    
    /**
     * Set the tempo sync note value (e.g., 1.0 = quarter note, 0.5 = eighth note).
     * 
     * @param noteValue The note value
     */
    void setTempoSyncNote(float noteValue);
    
    /**
     * Get the tempo sync note value.
     * 
     * @return The note value
     */
    float getTempoSyncNote() const;
    
    /**
     * Set the host BPM.
     * 
     * @param bpm The host BPM
     */
    void setHostBPM(double bpm);
    
    /**
     * Get the host BPM.
     * 
     * @return The host BPM
     */
    double getHostBPM() const;
    
    /**
     * Set the custom waveform.
     * 
     * @param waveform The custom waveform (array of values from -1.0 to 1.0)
     * @param size The size of the waveform array
     */
    void setCustomWaveform(const float* waveform, int size);
    
    /**
     * Trigger the LFO (for Note sync mode).
     */
    void trigger();
    
    /**
     * Set the retrigger phase (0.0 to 1.0).
     * 
     * @param phase The retrigger phase
     */
    void setRetriggerPhase(float phase);
    
    /**
     * Get the retrigger phase.
     * 
     * @return The retrigger phase
     */
    float getRetriggerPhase() const;
    
private:
    WaveformType waveformType = Sine;
    SyncMode syncMode = Free;
    float frequency = 1.0f; // Hz
    float tempoSyncNote = 1.0f; // 1.0 = quarter note
    double hostBPM = 120.0;
    float retriggerPhase = 0.0f;
    bool triggered = false;
    
    // Custom waveform
    std::vector<float> customWaveform;
    
    /**
     * Calculate the value for a specific waveform type and phase.
     * 
     * @param type The waveform type
     * @param phase The phase (0.0 to 1.0)
     * @return The waveform value at the specified phase
     */
    float calculateWaveformValue(WaveformType type, float phase) const;
    
    /**
     * Calculate the effective frequency based on sync mode.
     * 
     * @return The effective frequency in Hz
     */
    float calculateEffectiveFrequency() const;
    
    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(LFO)
};
