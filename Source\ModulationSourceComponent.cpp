/*
  ==============================================================================

    ModulationSourceComponent.cpp
    Created: 21 May 2023
    Author:  AuraBloom Team

    Implementation of the ModulationSourceComponent class.

  ==============================================================================
*/

#include "ModulationSourceComponent.h"

ModulationSourceComponent::ModulationSourceComponent(ModulationSource* source)
    : source(source)
{
    // Start timer for animation
    startTimerHz(30);
}

ModulationSourceComponent::~ModulationSourceComponent()
{
    // Stop timer
    stopTimer();
}

void ModulationSourceComponent::paint(juce::Graphics& g)
{
    // Fill background
    g.fillAll(juce::Colour(40, 40, 80));
    
    // Draw border
    g.setColour(active ? juce::Colour(100, 100, 255) : juce::Colour(60, 60, 100));
    g.drawRoundedRectangle(getLocalBounds().toFloat().reduced(1.0f), 4.0f, 2.0f);
    
    // Get component areas
    auto bounds = getLocalBounds().reduced(4);
    auto headerBounds = bounds.removeFromTop(30);
    auto waveformBounds = bounds.removeFromTop(bounds.getHeight() / 2);
    auto controlsBounds = bounds;
    
    // Draw header, waveform, and controls
    drawHeader(g, headerBounds);
    drawWaveform(g, waveformBounds);
    drawControls(g, controlsBounds);
}

void ModulationSourceComponent::resized()
{
    // Nothing to do here, subclasses will handle control layout
}

ModulationSource* ModulationSourceComponent::getModulationSource() const
{
    return source;
}

void ModulationSourceComponent::setModulationSource(ModulationSource* source)
{
    this->source = source;
    repaint();
}

void ModulationSourceComponent::setActive(bool active)
{
    this->active = active;
    repaint();
}

bool ModulationSourceComponent::isActive() const
{
    return active;
}

void ModulationSourceComponent::drawWaveform(juce::Graphics& g, juce::Rectangle<int> bounds)
{
    if (source == nullptr)
        return;
    
    // Set up colors
    g.setColour(juce::Colour(30, 30, 60));
    g.fillRoundedRectangle(bounds.toFloat(), 4.0f);
    
    // Draw center line
    g.setColour(juce::Colour(60, 60, 100));
    auto centerY = bounds.getCentreY();
    g.drawLine(bounds.getX(), centerY, bounds.getRight(), centerY, 1.0f);
    
    // Draw waveform
    g.setColour(active ? juce::Colour(100, 100, 255) : juce::Colour(80, 80, 120));
    
    juce::Path waveformPath;
    bool pathStarted = false;
    
    // Sample the modulation source at different time offsets
    for (int x = 0; x < bounds.getWidth(); ++x)
    {
        float timeOffset = (float)x / (float)bounds.getWidth() * 1.0f; // 1 second of waveform
        float value = source->getValueAt(timeOffset);
        
        // Map value to y coordinate
        float y = centerY - value * bounds.getHeight() * 0.4f;
        
        if (!pathStarted)
        {
            waveformPath.startNewSubPath(bounds.getX() + x, y);
            pathStarted = true;
        }
        else
        {
            waveformPath.lineTo(bounds.getX() + x, y);
        }
    }
    
    // Draw the path
    g.strokePath(waveformPath, juce::PathStrokeType(2.0f));
}

void ModulationSourceComponent::drawControls(juce::Graphics& g, juce::Rectangle<int> bounds)
{
    // Base implementation does nothing, subclasses will override
}

void ModulationSourceComponent::drawHeader(juce::Graphics& g, juce::Rectangle<int> bounds)
{
    if (source == nullptr)
        return;
    
    // Draw source name
    g.setColour(juce::Colours::white);
    g.setFont(juce::FontOptions (16.0f));
    g.drawText(source->getName(), bounds, juce::Justification::centred, true);
}

void ModulationSourceComponent::timerCallback()
{
    // Repaint to update waveform animation
    repaint();
}
