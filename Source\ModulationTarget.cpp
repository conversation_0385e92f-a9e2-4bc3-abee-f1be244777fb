/*
  ==============================================================================

    ModulationTarget.cpp
    Created: 21 May 2023
    Author:  AuraBloom Team

    Implementation of the ModulationTarget class.

  ==============================================================================
*/

#include "ModulationTarget.h"

ModulationTarget::ModulationTarget(const juce::String& name)
    : name(name)
{
}

ModulationTarget::~ModulationTarget()
{
}

const juce::String& ModulationTarget::getName() const
{
    return name;
}

void ModulationTarget::setName(const juce::String& name)
{
    this->name = name;
}

float ModulationTarget::getValue() const
{
    return currentValue;
}

void ModulationTarget::setBaseValue(float value)
{
    baseValue = juce::jlimit(minValue, maxValue, value);
    update();
}

float ModulationTarget::getBaseValue() const
{
    return baseValue;
}

void ModulationTarget::setModulationAmount(float amount)
{
    modulationAmount = juce::jlimit(-1.0f, 1.0f, amount);
    update();
}

float ModulationTarget::getModulationAmount() const
{
    return modulationAmount;
}

void ModulationTarget::setModulationValue(float value)
{
    modulationValue = juce::jlimit(-1.0f, 1.0f, value);
    update();
}

float ModulationTarget::getModulationValue() const
{
    return modulationValue;
}

void ModulationTarget::setMinValue(float minValue)
{
    this->minValue = minValue;
    update();
}

float ModulationTarget::getMinValue() const
{
    return minValue;
}

void ModulationTarget::setMaxValue(float maxValue)
{
    this->maxValue = maxValue;
    update();
}

float ModulationTarget::getMaxValue() const
{
    return maxValue;
}

void ModulationTarget::setBipolar(bool bipolar)
{
    this->bipolar = bipolar;
    update();
}

bool ModulationTarget::isBipolar() const
{
    return bipolar;
}

void ModulationTarget::update()
{
    // Calculate the modulation offset
    float modulationOffset = modulationValue * modulationAmount;
    
    if (bipolar)
    {
        // For bipolar targets, the modulation is applied symmetrically around the base value
        float normalizedBase = (baseValue - minValue) / (maxValue - minValue) * 2.0f - 1.0f;
        float normalizedValue = juce::jlimit(-1.0f, 1.0f, normalizedBase + modulationOffset);
        currentValue = (normalizedValue + 1.0f) * 0.5f * (maxValue - minValue) + minValue;
    }
    else
    {
        // For unipolar targets, the modulation is applied relative to the base value
        float normalizedBase = (baseValue - minValue) / (maxValue - minValue);
        float normalizedValue = juce::jlimit(0.0f, 1.0f, normalizedBase + modulationOffset);
        currentValue = normalizedValue * (maxValue - minValue) + minValue;
    }
}
