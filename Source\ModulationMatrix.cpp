/*
  ==============================================================================

    ModulationMatrix.cpp
    Created: 21 May 2023
    Author:  AuraBloom Team

    Implementation of the ModulationMatrix class.

  ==============================================================================
*/

#include "ModulationMatrix.h"
#include "PluginParameters.h"

ModulationMatrix::ModulationMatrix()
{
}

ModulationMatrix::~ModulationMatrix()
{
    // Clear connections first to avoid dangling pointers
    connections.clear();

    // Clear sources and targets
    sources.clear();
    targets.clear();

    // Owned sources and targets will be automatically deleted
}

bool ModulationMatrix::addSource(ModulationSource* source, bool takeOwnership)
{
    if (source == nullptr)
        return false;

    // Check if source already exists
    for (auto* existingSource : sources)
    {
        if (existingSource == source)
            return false;
    }

    // Add source
    sources.push_back(source);

    // Take ownership if requested
    if (takeOwnership)
    {
        ownedSources.push_back(std::unique_ptr<ModulationSource>(source));
    }

    return true;
}

bool ModulationMatrix::removeSource(ModulationSource* source)
{
    if (source == nullptr)
        return false;

    // Remove connections involving this source
    connections.erase(
        std::remove_if(connections.begin(), connections.end(),
                      [source](const ModulationConnection& connection) {
                          return connection.source == source;
                      }),
        connections.end());

    // Remove source from sources list
    auto it = std::find(sources.begin(), sources.end(), source);
    if (it != sources.end())
    {
        sources.erase(it);

        // Remove from owned sources if owned
        for (auto ownedIt = ownedSources.begin(); ownedIt != ownedSources.end(); ++ownedIt)
        {
            if (ownedIt->get() == source)
            {
                ownedSources.erase(ownedIt);
                break;
            }
        }

        return true;
    }

    return false;
}

ModulationSource* ModulationMatrix::getSource(const juce::String& name) const
{
    for (auto* source : sources)
    {
        if (source->getName() == name)
            return source;
    }

    return nullptr;
}

const std::vector<ModulationSource*>& ModulationMatrix::getSources() const
{
    return sources;
}

bool ModulationMatrix::addTarget(ModulationTarget* target, bool takeOwnership)
{
    if (target == nullptr)
        return false;

    // Check if target already exists
    for (auto* existingTarget : targets)
    {
        if (existingTarget == target)
            return false;
    }

    // Add target
    targets.push_back(target);

    // Take ownership if requested
    if (takeOwnership)
    {
        ownedTargets.push_back(std::unique_ptr<ModulationTarget>(target));
    }

    return true;
}

bool ModulationMatrix::removeTarget(ModulationTarget* target)
{
    if (target == nullptr)
        return false;

    // Remove connections involving this target
    connections.erase(
        std::remove_if(connections.begin(), connections.end(),
                      [target](const ModulationConnection& connection) {
                          return connection.target == target;
                      }),
        connections.end());

    // Remove target from targets list
    auto it = std::find(targets.begin(), targets.end(), target);
    if (it != targets.end())
    {
        targets.erase(it);

        // Remove from owned targets if owned
        for (auto ownedIt = ownedTargets.begin(); ownedIt != ownedTargets.end(); ++ownedIt)
        {
            if (ownedIt->get() == target)
            {
                ownedTargets.erase(ownedIt);
                break;
            }
        }

        return true;
    }

    return false;
}

ModulationTarget* ModulationMatrix::getTarget(const juce::String& name) const
{
    for (auto* target : targets)
    {
        if (target->getName() == name)
            return target;
    }

    return nullptr;
}

ParameterModulationTarget* ModulationMatrix::getParameterTarget(const juce::String& parameterID) const
{
    for (auto* target : targets)
    {
        auto* paramTarget = dynamic_cast<ParameterModulationTarget*>(target);
        if (paramTarget != nullptr && paramTarget->getParameterID() == parameterID)
            return paramTarget;
    }

    return nullptr;
}

const std::vector<ModulationTarget*>& ModulationMatrix::getTargets() const
{
    return targets;
}

bool ModulationMatrix::createConnection(ModulationSource* source, ModulationTarget* target, float amount, bool enabled)
{
    if (source == nullptr || target == nullptr)
        return false;

    // Check if connection already exists
    for (auto& connection : connections)
    {
        if (connection.source == source && connection.target == target)
        {
            // Update existing connection
            connection.amount = amount;
            connection.enabled = enabled;
            return true;
        }
    }

    // Create new connection
    connections.emplace_back(source, target, amount, enabled);
    return true;
}

bool ModulationMatrix::createConnection(const juce::String& sourceName, const juce::String& targetName, float amount, bool enabled)
{
    ModulationSource* source = getSource(sourceName);
    ModulationTarget* target = getTarget(targetName);

    if (source == nullptr || target == nullptr)
        return false;

    return createConnection(source, target, amount, enabled);
}

bool ModulationMatrix::removeConnection(ModulationSource* source, ModulationTarget* target)
{
    if (source == nullptr || target == nullptr)
        return false;

    // Find and remove connection
    auto it = std::find_if(connections.begin(), connections.end(),
                          [source, target](const ModulationConnection& connection) {
                              return connection.source == source && connection.target == target;
                          });

    if (it != connections.end())
    {
        connections.erase(it);
        return true;
    }

    return false;
}

bool ModulationMatrix::removeConnection(const juce::String& sourceName, const juce::String& targetName)
{
    ModulationSource* source = getSource(sourceName);
    ModulationTarget* target = getTarget(targetName);

    if (source == nullptr || target == nullptr)
        return false;

    return removeConnection(source, target);
}

const std::vector<ModulationConnection>& ModulationMatrix::getConnections() const
{
    return connections;
}

std::vector<ModulationConnection> ModulationMatrix::getConnectionsForSource(ModulationSource* source) const
{
    std::vector<ModulationConnection> result;

    for (const auto& connection : connections)
    {
        if (connection.source == source)
            result.push_back(connection);
    }

    return result;
}

std::vector<ModulationConnection> ModulationMatrix::getConnectionsForTarget(ModulationTarget* target) const
{
    std::vector<ModulationConnection> result;

    for (const auto& connection : connections)
    {
        if (connection.target == target)
            result.push_back(connection);
    }

    return result;
}

void ModulationMatrix::updateSources(float deltaTime)
{
    // Update all modulation sources
    for (auto* source : sources)
    {
        if (source->isActive())
        {
            source->update(deltaTime);
        }
    }
}

void ModulationMatrix::applyModulation()
{
    // Reset all targets to their base values
    for (auto* target : targets)
    {
        target->setModulationValue(0.0f);
    }

    // Apply modulation from all connections
    for (const auto& connection : connections)
    {
        if (connection.enabled && connection.source->isActive())
        {
            // Get modulation value from source
            float modulationValue = connection.source->getValue();

            // Apply modulation to target
            connection.target->setModulationValue(modulationValue);
            connection.target->setModulationAmount(connection.amount);
            connection.target->update();
            connection.target->applyValue();
        }
    }
}

void ModulationMatrix::reset()
{
    // Reset all modulation sources
    for (auto* source : sources)
    {
        source->reset();
    }

    // Reset all targets to their base values
    for (auto* target : targets)
    {
        target->setModulationValue(0.0f);
        target->update();
        target->applyValue();
    }
}

void ModulationMatrix::createParameterTargets(juce::AudioProcessorValueTreeState& apvts)
{
    // Create parameter targets for all parameters in the APVTS
    // Instead of using getParameters() which doesn't exist, we'll iterate through known parameter IDs

    // Get all parameter IDs from AuraBloomParameters namespace
    std::vector<juce::String> paramIDs = {
        AuraBloomParameters::MIX_ID,
        AuraBloomParameters::GAIN_ID,
        AuraBloomParameters::EMITTER_AUDIO_INPUT_ID,
        AuraBloomParameters::EMITTER_SHAPE_ID,
        AuraBloomParameters::EMITTER_RATE_ID,
        AuraBloomParameters::EMITTER_BURST_MODE_ID,
        AuraBloomParameters::EMITTER_INITIAL_VELOCITY_ID,
        AuraBloomParameters::EMITTER_SPREAD_ID,
        AuraBloomParameters::BUFFER_PLAYBACK_POSITION_ID,
        AuraBloomParameters::BUFFER_SCAN_SPEED_ID,
        AuraBloomParameters::BUFFER_LOOP_MODE_ID,
        AuraBloomParameters::GRAIN_SIZE_ID,
        AuraBloomParameters::GRAIN_ENVELOPE_ID,
        AuraBloomParameters::GRAIN_PITCH_SHIFT_ID,
        AuraBloomParameters::GRAIN_PAN_ID,
        AuraBloomParameters::GRAIN_DENSITY_ID,
        AuraBloomParameters::GRAIN_PLAY_DIRECTION_ID,
        AuraBloomParameters::PARTICLE_LIFESPAN_ID,
        AuraBloomParameters::PARTICLE_BEHAVIOR_MODE_ID,
        AuraBloomParameters::PARTICLE_BEHAVIOR_INTENSITY_ID,
        AuraBloomParameters::PARTICLE_INTERACTION_RADIUS_ID,
        AuraBloomParameters::PARTICLE_DAMPING_ID
    };

    // Create parameter targets for each parameter ID
    for (const auto& paramID : paramIDs)
    {
        auto* param = apvts.getParameter(paramID);
        if (param != nullptr)
        {
            auto* rangedParam = dynamic_cast<juce::RangedAudioParameter*>(param);
            if (rangedParam != nullptr)
            {
                // Check if target already exists
                if (getParameterTarget(paramID) == nullptr)
                {
                    // Create new parameter target
                    auto* target = new ParameterModulationTarget(rangedParam, paramID);
                    addTarget(target, true);
                }
            }
        }
    }
}
