#pragma once

#include <JuceHeader.h>
#include <chrono>
#include <deque>

/**
 * 性能监控系统
 * 监控CPU使用率、内存使用、活跃粒子数等关键指标
 */
class PerformanceMonitor
{
public:
    PerformanceMonitor();
    ~PerformanceMonitor();

    // 性能测量
    void startFrame();
    void endFrame();
    void updateMetrics(int activeParticles, int totalParticles);

    // 性能查询
    float getCpuUsage() const { return cpuUsage; }
    float getAverageFrameTime() const { return averageFrameTime; }
    int getActiveParticles() const { return activeParticles; }
    int getTotalParticles() const { return totalParticles; }
    float getMemoryUsageMB() const { return memoryUsageMB; }

    // 性能控制
    bool shouldReduceComplexity() const;
    bool shouldLimitParticles() const;
    int getRecommendedMaxParticles() const;
    float getComplexityReduction() const;

    // 配置
    void setTargetCpuUsage(float target) { targetCpuUsage = target; }
    void setMaxFrameTime(float maxTime) { maxFrameTime = maxTime; }
    void setAdaptiveMode(bool enabled) { adaptiveMode = enabled; }

    // 统计信息
    struct Stats
    {
        float minFrameTime = 0.0f;
        float maxFrameTime = 0.0f;
        float avgFrameTime = 0.0f;
        float cpuUsage = 0.0f;
        int peakParticles = 0;
        int droppedFrames = 0;
    };

    const Stats& getStats() const { return stats; }
    void resetStats();

private:
    // 时间测量
    std::chrono::high_resolution_clock::time_point frameStartTime;
    std::deque<float> frameTimeHistory;
    static constexpr int MAX_HISTORY_SIZE = 60; // 1秒历史(60fps)

    // 性能指标
    float cpuUsage = 0.0f;
    float averageFrameTime = 0.0f;
    int activeParticles = 0;
    int totalParticles = 0;
    float memoryUsageMB = 0.0f;

    // 性能目标
    float targetCpuUsage = 15.0f; // 目标CPU使用率(%)
    float maxFrameTime = 16.67f; // 最大帧时间(ms) - 60fps
    bool adaptiveMode = true;

    // 自适应控制
    float complexityReduction = 1.0f; // 1.0 = 无减少, 0.5 = 50%减少
    int recommendedMaxParticles = 1000;
    
    // 统计信息
    Stats stats;
    int frameCount = 0;

    // 内部方法
    void updateFrameTimeHistory(float frameTime);
    void calculateCpuUsage();
    void updateAdaptiveSettings();
    void updateMemoryUsage();
};
