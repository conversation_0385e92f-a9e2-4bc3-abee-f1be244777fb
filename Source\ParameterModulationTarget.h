/*
  ==============================================================================

    ParameterModulationTarget.h
    Created: 21 May 2023
    Author:  AuraBloom Team

    This file defines the ParameterModulationTarget class, which represents a
    parameter that can be modulated by modulation sources.

  ==============================================================================
*/

#pragma once

#include <JuceHeader.h>
#include "ModulationTarget.h"

/**
 * A modulation target that modulates an AudioProcessorParameter.
 */
class ParameterModulationTarget : public ModulationTarget
{
public:
    /**
     * Constructor.
     * 
     * @param parameter The parameter to modulate
     * @param parameterID The ID of the parameter
     */
    ParameterModulationTarget(juce::RangedAudioParameter* parameter, const juce::String& parameterID);
    
    /**
     * Destructor.
     */
    ~ParameterModulationTarget() override;
    
    /**
     * Apply the current value to the parameter.
     */
    void applyValue() override;
    
    /**
     * Get the parameter ID.
     * 
     * @return The parameter ID
     */
    const juce::String& getParameterID() const;
    
    /**
     * Get the parameter.
     * 
     * @return The parameter
     */
    juce::RangedAudioParameter* getParameter() const;
    
    /**
     * Update the base value from the parameter.
     */
    void updateBaseValueFromParameter();
    
private:
    juce::RangedAudioParameter* parameter;
    juce::String parameterID;
    
    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(ParameterModulationTarget)
};
