@echo off
echo ========================================
echo AuraBloom构建环境验证
echo ========================================

echo 检查Visual Studio 2022...
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
    echo [OK] Visual Studio 2022 Community 已安装
) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    echo [OK] Visual Studio 2022 Professional 已安装
) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe" (
    echo [OK] Visual Studio 2022 Enterprise 已安装
) else (
    echo [ERROR] Visual Studio 2022 未找到!
    echo 请安装Visual Studio 2022并包含C++开发工具
)

echo.
echo 检查JUCE Framework...
if exist "C:\JUCE\modules" (
    echo [OK] JUCE Framework 已安装
) else (
    echo [WARNING] JUCE Framework 未在默认位置找到
    echo 请确保JUCE安装在 C:\JUCE\ 或更新项目文件中的路径
)

echo.
echo 检查项目文件...
if exist "Builds\AuraBloom.sln" (
    echo [OK] Visual Studio解决方案文件存在
) else (
    echo [ERROR] 解决方案文件未找到!
)

if exist "Source\PluginProcessor.cpp" (
    echo [OK] 源代码文件存在
) else (
    echo [ERROR] 源代码文件未找到!
)

echo.
echo ========================================
echo 验证完成
echo ========================================
pause
