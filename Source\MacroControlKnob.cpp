/*
  ==============================================================================

    MacroControlKnob.cpp
    Created: 21 May 2023
    Author:  AuraBloom Team

    宏控制旋钮的实现

  ==============================================================================
*/

#include "MacroControlKnob.h"

MacroControlKnob::MacroControlKnob(MacroType type, const juce::String& name)
    : macroType(type), macroName(name)
{
    setInterceptsMouseClicks(true, true);
    setupDefaultMappings();
}

MacroControlKnob::~MacroControlKnob()
{
}

void MacroControlKnob::paint(juce::Graphics& g)
{
    auto bounds = getLocalBounds().toFloat();

    // 重新设计布局 - 旋钮在上方，文本在下方有足够空间
    auto totalHeight = bounds.getHeight();
    auto knobHeight = totalHeight * 0.55f; // 旋钮占55%

    auto knobArea = bounds.removeFromTop(knobHeight);
    // 让旋钮稍微小一点，留出更多空间
    auto actualKnobSize = juce::jmin(knobSize * 0.9f, knobArea.getWidth() * 0.8f, knobArea.getHeight() * 0.8f);
    knobArea = knobArea.withSizeKeepingCentre(actualKnobSize, actualKnobSize);

    // 绘制光晕效果
    if (isDragging)
        drawGlow(g, knobArea);

    // 绘制旋钮背景
    drawKnobBackground(g, knobArea);

    // 绘制旋钮指示器
    drawKnobIndicator(g, knobArea);

    // 文本区域 - 剩余的45%空间
    auto textArea = bounds;
    textArea.removeFromTop(8); // 与旋钮的间距

    if (showValue)
    {
        auto valueArea = textArea.removeFromTop(18); // 数值区域
        drawValueText(g, valueArea);
        textArea.removeFromTop(3); // 间距
    }

    if (showName)
    {
        auto nameArea = textArea.removeFromTop(16); // 名称区域
        drawNameText(g, nameArea);
        textArea.removeFromTop(3); // 间距

        // 描述区域
        if (textArea.getHeight() > 12)
        {
            auto descArea = textArea.removeFromTop(14); // 描述区域
            drawDescriptionText(g, descArea);
        }
    }
}

void MacroControlKnob::drawKnobBackground(juce::Graphics& g, juce::Rectangle<float> knobArea)
{
    auto center = knobArea.getCentre();
    auto radius = knobArea.getWidth() * 0.5f;

    // 外圈渐变
    juce::ColourGradient outerGradient(
        juce::Colour(60, 60, 80),
        center.x, center.y - radius,
        juce::Colour(30, 30, 50),
        center.x, center.y + radius,
        false
    );

    g.setGradientFill(outerGradient);
    g.fillEllipse(knobArea);

    // 内圈
    auto innerArea = knobArea.reduced(radius * 0.15f);
    auto innerRadius = innerArea.getWidth() * 0.5f;

    juce::ColourGradient innerGradient(
        juce::Colour(40, 40, 60),
        center.x, center.y - innerRadius,
        juce::Colour(20, 20, 40),
        center.x, center.y + innerRadius,
        false
    );

    g.setGradientFill(innerGradient);
    g.fillEllipse(innerArea);

    // 边框
    g.setColour(getMacroColor().withAlpha(0.6f));
    g.drawEllipse(knobArea, 2.0f);

    // 内边框
    g.setColour(juce::Colour(80, 80, 100));
    g.drawEllipse(innerArea, 1.0f);
}

void MacroControlKnob::drawKnobIndicator(juce::Graphics& g, juce::Rectangle<float> knobArea)
{
    auto center = knobArea.getCentre();
    auto radius = knobArea.getWidth() * 0.4f;

    // 计算角度 (从-135度到+135度，总共270度)
    float startAngle = -2.356f; // -135度
    float endAngle = 2.356f;    // +135度
    float currentAngle = startAngle + (endAngle - startAngle) * currentValue;

    // 绘制刻度弧
    g.setColour(juce::Colour(100, 100, 120));
    juce::Path arcPath;
    arcPath.addCentredArc(center.x, center.y, radius, radius, 0.0f, startAngle, endAngle, true);
    g.strokePath(arcPath, juce::PathStrokeType(2.0f));

    // 绘制值弧
    g.setColour(getMacroColor());
    juce::Path valuePath;
    valuePath.addCentredArc(center.x, center.y, radius, radius, 0.0f, startAngle, currentAngle, true);
    g.strokePath(valuePath, juce::PathStrokeType(3.0f));

    // 绘制指示器线
    float indicatorLength = radius * 0.7f;
    float indicatorX = center.x + indicatorLength * std::cos(currentAngle);
    float indicatorY = center.y + indicatorLength * std::sin(currentAngle);

    g.setColour(getMacroColor().brighter(0.3f));
    g.drawLine(center.x, center.y, indicatorX, indicatorY, 3.0f);

    // 中心点
    g.setColour(getMacroColor().brighter(0.5f));
    g.fillEllipse(center.x - 3, center.y - 3, 6, 6);
}

void MacroControlKnob::drawValueText(juce::Graphics& g, juce::Rectangle<float> area)
{
    g.setColour(juce::Colours::white.withAlpha(0.95f));
    g.setFont(13.0f);

    juce::String valueText = juce::String(currentValue * 100.0f, 0) + "%";
    g.drawText(valueText, area, juce::Justification::centred);
}

void MacroControlKnob::drawNameText(juce::Graphics& g, juce::Rectangle<float> area)
{
    g.setColour(getMacroColor().withAlpha(0.9f));
    g.setFont(11.0f);

    // 调试：确保名称不为空
    juce::String displayName = macroName.isEmpty() ? "UNNAMED" : macroName;
    g.drawText(displayName, area, juce::Justification::centred);
}

void MacroControlKnob::drawDescriptionText(juce::Graphics& g, juce::Rectangle<float> area)
{
    g.setColour(juce::Colours::white.withAlpha(0.7f));
    g.setFont(9.0f);

    g.drawText(getMacroDescription(), area, juce::Justification::centred);
}

void MacroControlKnob::drawGlow(juce::Graphics& g, juce::Rectangle<float> knobArea)
{
    auto center = knobArea.getCentre();
    auto glowRadius = knobArea.getWidth() * 0.7f;

    juce::ColourGradient glowGradient(
        getMacroColor().withAlpha(0.3f),
        center.x, center.y,
        getMacroColor().withAlpha(0.0f),
        center.x + glowRadius, center.y,
        true
    );

    g.setGradientFill(glowGradient);
    g.fillEllipse(center.x - glowRadius, center.y - glowRadius,
                  glowRadius * 2, glowRadius * 2);
}

void MacroControlKnob::resized()
{
    // 根据组件大小调整旋钮大小 - 更智能的计算
    auto bounds = getLocalBounds();

    // 考虑到文本需要的空间，旋钮区域大约占总高度的55%
    auto availableKnobHeight = bounds.getHeight() * 0.55f;
    auto availableKnobWidth = bounds.getWidth() * 0.8f; // 留出一些边距

    knobSize = juce::jmin(availableKnobWidth, availableKnobHeight);
    knobSize = juce::jlimit(35.0f, 70.0f, knobSize);
}

void MacroControlKnob::mouseDown(const juce::MouseEvent& e)
{
    isDragging = true;
    dragStartValue = currentValue;
    dragStartPosition = e.position;
    repaint();
}

void MacroControlKnob::mouseDrag(const juce::MouseEvent& e)
{
    if (isDragging)
    {
        // 垂直拖拽控制
        float dragDistance = dragStartPosition.y - e.position.y;
        float sensitivity = 0.005f; // 调整灵敏度

        float newValue = dragStartValue + dragDistance * sensitivity;
        setValue(juce::jlimit(minValue, maxValue, newValue));
    }
}

void MacroControlKnob::mouseUp(const juce::MouseEvent& e)
{
    isDragging = false;
    repaint();
}

void MacroControlKnob::mouseWheelMove(const juce::MouseEvent& e, const juce::MouseWheelDetails& wheel)
{
    float increment = wheel.deltaY * 0.1f;
    setValue(juce::jlimit(minValue, maxValue, currentValue + increment));
}

void MacroControlKnob::setValue(float newValue, bool sendNotification)
{
    newValue = juce::jlimit(minValue, maxValue, newValue);

    if (currentValue != newValue)
    {
        currentValue = newValue;

        if (sendNotification)
        {
            updateMappedParameters();

            if (onValueChange)
                onValueChange(currentValue);
        }

        repaint();
    }
}

void MacroControlKnob::setRange(float min, float max)
{
    minValue = min;
    maxValue = max;
    setValue(currentValue, false); // 重新限制当前值
}

void MacroControlKnob::addParameterMapping(const juce::String& parameterId, float minVal, float maxVal,
                                         float curve, bool inverted)
{
    parameterMappings.emplace_back(parameterId, minVal, maxVal, curve, inverted);
}

void MacroControlKnob::clearParameterMappings()
{
    parameterMappings.clear();
}

void MacroControlKnob::updateMappedParameters()
{
    if (!onParameterChange)
        return;

    for (const auto& mapping : parameterMappings)
    {
        float normalizedValue = (currentValue - minValue) / (maxValue - minValue);

        if (mapping.inverted)
            normalizedValue = 1.0f - normalizedValue;

        // 应用曲线
        float curvedValue = applyParameterCurve(normalizedValue, mapping.curve);

        // 映射到目标范围
        float mappedValue = mapping.minValue + (mapping.maxValue - mapping.minValue) * curvedValue;

        onParameterChange(mapping.parameterId, mappedValue);
    }
}

float MacroControlKnob::applyParameterCurve(float normalizedValue, float curve) const
{
    if (curve == 1.0f)
        return normalizedValue; // 线性

    return std::pow(normalizedValue, curve);
}

juce::Colour MacroControlKnob::getMacroColor() const
{
    switch (macroType)
    {
        case Genesis: return juce::Colour(100, 255, 150); // 生命绿
        case Scatter: return juce::Colour(255, 180, 100); // 散射橙
        case Form:    return juce::Colour(150, 150, 255); // 形态蓝
        case Chaos:   return juce::Colour(255, 100, 150); // 混沌红
        default:      return juce::Colours::white;
    }
}

juce::String MacroControlKnob::getMacroDescription() const
{
    switch (macroType)
    {
        case Genesis: return "Life";
        case Scatter: return "Space";
        case Form:    return "Shape";
        case Chaos:   return "Random";
        default:      return "";
    }
}

void MacroControlKnob::setupDefaultMappings()
{
    clearParameterMappings();

    switch (macroType)
    {
        case Genesis: setupGenesisMappings(); break;
        case Scatter: setupScatterMappings(); break;
        case Form:    setupFormMappings(); break;
        case Chaos:   setupChaosMappings(); break;
    }
}

void MacroControlKnob::setupGenesisMappings()
{
    // Genesis - 创世：控制整体生命力和密度
    addParameterMapping("density", 0.1f, 1.0f, 1.5f);      // 密度，指数曲线
    addParameterMapping("rate", 0.5f, 8.0f, 1.2f);         // 发射速率
    addParameterMapping("grainSize", 20.0f, 150.0f, 0.8f); // 颗粒大小，对数曲线
    addParameterMapping("mix", 0.3f, 1.0f, 1.0f);          // 混合比例
}

void MacroControlKnob::setupScatterMappings()
{
    // Scatter - 散射：控制空间和时间分布
    addParameterMapping("spread", 0.0f, 1.0f, 1.0f);       // 立体声展开
    addParameterMapping("pitch", -6.0f, 6.0f, 1.0f);       // 音高散射
    addParameterMapping("texture", 0.0f, 0.8f, 1.3f);      // 纹理随机性
    addParameterMapping("rate", 1.0f, 5.0f, 1.0f);         // 时间散射
}

void MacroControlKnob::setupFormMappings()
{
    // Form - 形态：控制颗粒形状和纹理
    addParameterMapping("grainSize", 10.0f, 200.0f, 1.0f); // 颗粒大小
    addParameterMapping("texture", 0.0f, 1.0f, 1.0f);      // 纹理复杂度
    addParameterMapping("density", 0.2f, 0.8f, 1.0f);      // 形态密度
    // 这里可以添加包络形状参数
}

void MacroControlKnob::setupChaosMappings()
{
    // Chaos - 混沌：控制随机性和不可预测性
    addParameterMapping("texture", 0.0f, 1.0f, 2.0f);      // 强烈的纹理变化
    addParameterMapping("pitch", -12.0f, 12.0f, 1.0f);     // 极端音高变化
    addParameterMapping("rate", 0.1f, 10.0f, 1.5f);        // 不规律的速率
    addParameterMapping("spread", 0.5f, 1.0f, 1.0f);       // 空间混沌
}
