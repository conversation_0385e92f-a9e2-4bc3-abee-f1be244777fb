/*
  ==============================================================================

    ParticleSystem.cpp
    Created: 21 May 2023
    Author:  AuraBloom Team

    Implementation of the ParticleSystem class.

  ==============================================================================
*/

#include "ParticleSystem.h"
#include "ForceField.h"

ParticleSystem::ParticleSystem()
    : fft(10) // FFT order 10 (1024 points)
{
    // Initialize FFT data
    fftData.resize(2048); // 2 * fft size for complex numbers
    fftMagnitudes.resize(1024); // fft size

    // Clear FFT data
    std::fill(fftData.begin(), fftData.end(), 0.0f);
    std::fill(fftMagnitudes.begin(), fftMagnitudes.end(), 0.0f);
}

ParticleSystem::~ParticleSystem()
{
}

void ParticleSystem::initialize(int maxParticles, double sampleRate, int samplesPerBlock)
{
    // Initialize performance monitoring
    performanceMonitor.setTargetCpuUsage(15.0f);
    performanceMonitor.setMaxFrameTime(16.67f); // 60fps
    performanceMonitor.setAdaptiveMode(true);

    // Initialize particles
    particles.resize(maxParticles);

    // Store audio parameters
    this->sampleRate = sampleRate;
    this->samplesPerBlock = samplesPerBlock;

    // Initialize grain buffer (5 seconds at max)
    int bufferSize = static_cast<int>(sampleRate * 5.0);
    grainBuffer.setSize(2, bufferSize);
    grainBuffer.clear();

    // Clear grid
    for (auto& row : grid)
    {
        for (auto& cell : row)
        {
            cell.particleIndices.clear();
        }
    }

    // Reset all particles
    reset();
}

void ParticleSystem::reset()
{
    // Reset all particles to inactive
    for (auto& particle : particles)
    {
        particle.reset();
    }

    // Clear force fields
    clearForceFields();

    // Clear grid
    for (auto& row : grid)
    {
        for (auto& cell : row)
        {
            cell.particleIndices.clear();
        }
    }
}

void ParticleSystem::update(float deltaTime)
{
    // Start performance monitoring
    performanceMonitor.startFrame();

    // Get performance recommendations
    bool shouldReduce = performanceMonitor.shouldReduceComplexity();
    int maxParticles = performanceMonitor.getRecommendedMaxParticles();
    float complexityReduction = performanceMonitor.getComplexityReduction();

    // Update spatial partitioning grid
    updateGrid();

    // Count active particles for performance monitoring
    int activeCount = 0;

    // Update all particles with smooth performance scaling
    for (auto& particle : particles)
    {
        if (particle.isActive)
        {
            activeCount++;

            // FIXED: 移除概率性更新机制，确保所有活跃粒子都被更新
            // 简化性能控制，只使用时间缩放
            particle.update(deltaTime * complexityReduction);

            // Check if particle is still alive
            if (!particle.isAlive())
            {
                particle.isActive = false;
                activeCount--;
            }
        }
    }

    // Apply behavior based on current mode (with performance scaling)
    if (!shouldReduce || complexityReduction > 0.5f)
    {
        applyBehavior(deltaTime * complexityReduction);
    }

    // Apply force fields (reduced frequency if needed)
    if (!shouldReduce || (performanceMonitor.getStats().avgFrameTime < 12.0f))
    {
        applyForceFields(deltaTime);
    }

    // Handle collisions (skip if performance is critical)
    if (!shouldReduce)
    {
        handleCollisions();
    }

    // Handle boundaries (always do this as it's lightweight)
    handleBoundaries();

    // Particle count stabilization - 保持粒子数量稳定
    stabilizeParticleCount(activeCount);

    // Update performance metrics
    performanceMonitor.updateMetrics(activeCount, static_cast<int>(particles.size()));
    performanceMonitor.endFrame();
}

void ParticleSystem::processAudio(juce::AudioBuffer<float>& buffer, const juce::AudioBuffer<float>& inputBuffer)
{
    // Clear output buffer
    buffer.clear();

    // Process each active particle
    for (auto& particle : particles)
    {
        if (particle.isActive)
        {
            processGrain(particle, buffer, inputBuffer);
        }
    }
}

void ParticleSystem::emitParticle(float x, float y, float vx, float vy)
{
    try {
        // Validate input parameters
        if (!std::isfinite(x) || !std::isfinite(y) || !std::isfinite(vx) || !std::isfinite(vy))
        {
            DBG("Invalid particle parameters: x=" + juce::String(x) + " y=" + juce::String(y) +
                " vx=" + juce::String(vx) + " vy=" + juce::String(vy));
            return;
        }

        // Find an inactive particle
        int index = findInactiveParticle();

        if (index >= 0 && index < static_cast<int>(particles.size()))
        {
            // Initialize the particle
            Particle& particle = particles[index];
            particle.reset();

            particle.x = juce::jlimit(-200.0f, 200.0f, x);
            particle.y = juce::jlimit(-200.0f, 200.0f, y);
            particle.vx = juce::jlimit(-100.0f, 100.0f, vx);
            particle.vy = juce::jlimit(-100.0f, 100.0f, vy);
            particle.age = 0.0f;
            particle.isActive = true;
            particle.setState(ParticleState::Active);

            // Set default audio properties (these would normally come from parameters)
            particle.grainSize = 50.0f;
            particle.pitchShift = 0.0f;
            particle.pan = 0.0f;
            particle.playDirection = 0;
            particle.envelopeType = 0;
            particle.lifespan = 2.0f;
            particle.bufferPosition = 0;
            particle.bufferPlaybackRate = 1.0f;
        }
    }
    catch (const std::exception& e) {
        DBG("Exception in emitParticle: " + juce::String(e.what()));
    }
    catch (...) {
        DBG("Unknown exception in emitParticle");
    }
}

void ParticleSystem::emitParticleWithProperties(float x, float y, float vx, float vy,
                                              ParticleType type, float mass, float charge,
                                              float energy, float lifespan)
{
    // Find an inactive particle
    int index = findInactiveParticle();

    if (index >= 0)
    {
        // Initialize the particle
        Particle& particle = particles[index];
        particle.reset();

        particle.x = x;
        particle.y = y;
        particle.vx = vx;
        particle.vy = vy;
        particle.mass = mass;
        particle.charge = charge;
        particle.energy = energy;
        particle.lifespan = lifespan;
        particle.age = 0.0f;
        particle.isActive = true;
        particle.setState(ParticleState::Active);
        particle.setType(type);

        // Set audio properties based on particle properties
        particle.grainSize = 20.0f + mass * 30.0f; // 20-80ms based on mass
        particle.pitchShift = charge * 12.0f; // -12 to +12 semitones based on charge
        particle.pan = juce::jlimit(-1.0f, 1.0f, x / 100.0f); // Pan based on x position
        particle.playDirection = (vx > 0.0f) ? 0 : 1; // Direction based on x velocity
        particle.envelopeType = static_cast<int>(type) % 4; // Envelope based on type
        particle.bufferPosition = static_cast<int>((y + 100.0f) / 200.0f * 44100.0f * 5.0f); // Position based on y
        particle.bufferPlaybackRate = 0.5f + energy; // Playback rate based on energy
    }
}

Particle* ParticleSystem::createParticle()
{
    // Find an inactive particle
    int index = findInactiveParticle();

    if (index >= 0)
    {
        // Reset the particle
        particles[index].reset();
        return &particles[index];
    }

    return nullptr;
}

void ParticleSystem::setBehaviorMode(BehaviorMode mode)
{
    behaviorMode = mode;
}

void ParticleSystem::setBehaviorIntensity(float intensity)
{
    behaviorIntensity = juce::jlimit(0.0f, 1.0f, intensity);
}

void ParticleSystem::setInteractionRadius(float radius)
{
    interactionRadius = juce::jmax(1.0f, radius);
}

void ParticleSystem::setDamping(float damping)
{
    this->damping = juce::jlimit(0.0f, 1.0f, damping);
}

int ParticleSystem::getParticleCount() const
{
    return static_cast<int>(particles.size());
}

int ParticleSystem::getActiveParticleCount() const
{
    int count = 0;
    for (const auto& particle : particles)
    {
        if (particle.isActive)
        {
            count++;
        }
    }
    return count;
}

const std::vector<Particle>& ParticleSystem::getParticles() const
{
    // Return a safe reference to particles
    // Note: This should be called from the message thread only
    return particles;
}

void ParticleSystem::applyBehavior(float deltaTime)
{
    switch (behaviorMode)
    {
        case Swarm:
            applySwarmBehavior(deltaTime);
            break;
        case Repel:
            applyRepelBehavior(deltaTime);
            break;
        case Attract:
            applyAttractBehavior(deltaTime);
            break;
        case Orbit:
            applyOrbitBehavior(deltaTime);
            break;
        case Fluid:
            applyFluidBehavior(deltaTime);
            break;
        case Chaos:
            applyChaosBehavior(deltaTime);
            break;
        case Crystallize:
            applyCrystallizeBehavior(deltaTime);
            break;
        case Wave:
            applyWaveBehavior(deltaTime);
            break;
    }

    // Apply damping to all particles
    for (auto& particle : particles)
    {
        if (particle.isActive)
        {
            particle.vx *= (1.0f - damping);
            particle.vy *= (1.0f - damping);
        }
    }
}

void ParticleSystem::applySwarmBehavior(float deltaTime)
{
    // Optimized flocking behavior using spatial partitioning

    // Calculate center of mass and average velocity
    float centerX = 0.0f;
    float centerY = 0.0f;
    float avgVx = 0.0f;
    float avgVy = 0.0f;
    int activeCount = 0;

    for (const auto& particle : particles)
    {
        if (particle.isActive)
        {
            centerX += particle.x;
            centerY += particle.y;
            avgVx += particle.vx;
            avgVy += particle.vy;
            activeCount++;
        }
    }

    if (activeCount > 0)
    {
        centerX /= activeCount;
        centerY /= activeCount;
        avgVx /= activeCount;
        avgVy /= activeCount;

        // Apply forces to each particle using spatial grid
        for (auto& particle : particles)
        {
            if (particle.isActive)
            {
                // Alignment: steer towards average heading
                float alignForceX = (avgVx - particle.vx) * behaviorIntensity * 0.3f;
                float alignForceY = (avgVy - particle.vy) * behaviorIntensity * 0.3f;

                // Cohesion: steer towards center of mass
                float cohesionForceX = (centerX - particle.x) * behaviorIntensity * 0.01f;
                float cohesionForceY = (centerY - particle.y) * behaviorIntensity * 0.01f;

                // Separation: avoid crowding
                float separationForceX = 0.0f;
                float separationForceY = 0.0f;

                for (const auto& other : particles)
                {
                    if (other.isActive && &other != &particle)
                    {
                        float dx = particle.x - other.x;
                        float dy = particle.y - other.y;
                        float distSq = dx * dx + dy * dy;

                        if (distSq < interactionRadius * interactionRadius && distSq > 0.0001f)
                        {
                            float dist = std::sqrt(distSq);
                            float repelStrength = (interactionRadius - dist) / interactionRadius;
                            separationForceX += (dx / dist) * repelStrength;
                            separationForceY += (dy / dist) * repelStrength;
                        }
                    }
                }

                separationForceX *= behaviorIntensity * 0.05f;
                separationForceY *= behaviorIntensity * 0.05f;

                // Apply combined forces
                particle.applyForce(alignForceX + cohesionForceX + separationForceX,
                                   alignForceY + cohesionForceY + separationForceY);
            }
        }
    }
}

void ParticleSystem::applyRepelBehavior(float deltaTime)
{
    // Simple repulsion behavior
    for (auto& particle : particles)
    {
        if (particle.isActive)
        {
            for (auto& other : particles)
            {
                if (other.isActive && &other != &particle)
                {
                    float dx = particle.x - other.x;
                    float dy = particle.y - other.y;
                    float distSq = dx * dx + dy * dy;

                    if (distSq < interactionRadius * interactionRadius && distSq > 0.0001f)
                    {
                        float dist = std::sqrt(distSq);
                        float repelStrength = (interactionRadius - dist) / interactionRadius;
                        float forceX = (dx / dist) * repelStrength * behaviorIntensity * 0.1f;
                        float forceY = (dy / dist) * repelStrength * behaviorIntensity * 0.1f;

                        particle.applyForce(forceX, forceY);
                    }
                }
            }
        }
    }
}

void ParticleSystem::applyAttractBehavior(float deltaTime)
{
    // Simple attraction behavior
    for (auto& particle : particles)
    {
        if (particle.isActive)
        {
            for (auto& other : particles)
            {
                if (other.isActive && &other != &particle)
                {
                    float dx = other.x - particle.x;
                    float dy = other.y - particle.y;
                    float distSq = dx * dx + dy * dy;

                    if (distSq < interactionRadius * interactionRadius && distSq > 0.0001f)
                    {
                        float dist = std::sqrt(distSq);
                        float attractStrength = (interactionRadius - dist) / interactionRadius;
                        float forceX = (dx / dist) * attractStrength * behaviorIntensity * 0.05f;
                        float forceY = (dy / dist) * attractStrength * behaviorIntensity * 0.05f;

                        particle.applyForce(forceX, forceY);
                    }
                }
            }
        }
    }
}

void ParticleSystem::applyOrbitBehavior(float deltaTime)
{
    // Simple orbit behavior around center (0, 0)
    for (auto& particle : particles)
    {
        if (particle.isActive)
        {
            // Calculate distance from center
            float dx = particle.x;
            float dy = particle.y;
            float distSq = dx * dx + dy * dy;

            if (distSq > 0.0001f)
            {
                float dist = std::sqrt(distSq);

                // Calculate perpendicular force for orbit
                float forceX = -dy / dist * behaviorIntensity * 0.1f;
                float forceY = dx / dist * behaviorIntensity * 0.1f;

                // Add a small attraction to maintain orbit
                forceX += -dx / dist * behaviorIntensity * 0.01f;
                forceY += -dy / dist * behaviorIntensity * 0.01f;

                particle.applyForce(forceX, forceY);
            }
        }
    }
}

void ParticleSystem::applyFluidBehavior(float deltaTime)
{
    // Fluid-like behavior based on simplified Navier-Stokes
    for (auto& particle : particles)
    {
        if (particle.isActive)
        {
            // Calculate pressure and viscosity forces
            float pressureX = 0.0f;
            float pressureY = 0.0f;
            float viscosityX = 0.0f;
            float viscosityY = 0.0f;

            // Get grid cell for current particle
            int gridX, gridY;
            getGridCell(particle.x, particle.y, gridX, gridY);

            // Check neighboring cells
            for (int nx = juce::jmax(0, gridX - 1); nx <= juce::jmin(GRID_SIZE - 1, gridX + 1); ++nx)
            {
                for (int ny = juce::jmax(0, gridY - 1); ny <= juce::jmin(GRID_SIZE - 1, gridY + 1); ++ny)
                {
                    // Process particles in this cell
                    for (int otherIndex : grid[nx][ny].particleIndices)
                    {
                        Particle& other = particles[otherIndex];

                        if (&other != &particle && other.isActive)
                        {
                            float dx = particle.x - other.x;
                            float dy = particle.y - other.y;
                            float distSq = dx * dx + dy * dy;

                            if (distSq < interactionRadius * interactionRadius && distSq > 0.0001f)
                            {
                                float dist = std::sqrt(distSq);
                                float q = 1.0f - dist / interactionRadius;

                                // Pressure force (repulsive)
                                float pressure = q * q * behaviorIntensity * 0.1f;
                                pressureX += dx / dist * pressure;
                                pressureY += dy / dist * pressure;

                                // Viscosity force (velocity matching)
                                float viscosity = q * behaviorIntensity * 0.01f;
                                viscosityX += (other.vx - particle.vx) * viscosity;
                                viscosityY += (other.vy - particle.vy) * viscosity;
                            }
                        }
                    }
                }
            }

            // Apply combined forces
            particle.applyForceWithMass(pressureX + viscosityX, pressureY + viscosityY);
        }
    }
}

void ParticleSystem::applyChaosBehavior(float deltaTime)
{
    // Chaotic behavior with random forces and occasional state changes
    for (auto& particle : particles)
    {
        if (particle.isActive)
        {
            // Random force
            if (juce::Random::getSystemRandom().nextFloat() < 0.1f)
            {
                float forceX = (juce::Random::getSystemRandom().nextFloat() * 2.0f - 1.0f) * behaviorIntensity * 10.0f;
                float forceY = (juce::Random::getSystemRandom().nextFloat() * 2.0f - 1.0f) * behaviorIntensity * 10.0f;
                particle.applyForce(forceX, forceY);
            }

            // Occasional state change
            if (juce::Random::getSystemRandom().nextFloat() < 0.01f * behaviorIntensity)
            {
                int stateIndex = juce::Random::getSystemRandom().nextInt(4);
                particle.setState(static_cast<ParticleState>(stateIndex));
            }

            // Occasional type change
            if (juce::Random::getSystemRandom().nextFloat() < 0.005f * behaviorIntensity)
            {
                int typeIndex = juce::Random::getSystemRandom().nextInt(5);
                particle.setType(static_cast<ParticleType>(typeIndex));
            }

            // Occasional child particle emission
            if (juce::Random::getSystemRandom().nextFloat() < 0.002f * behaviorIntensity)
            {
                particle.emitChildParticles(*this, juce::Random::getSystemRandom().nextInt(3) + 1);
            }
        }
    }
}

void ParticleSystem::applyCrystallizeBehavior(float deltaTime)
{
    // Crystallization behavior - particles form grid-like structures
    for (auto& particle : particles)
    {
        if (particle.isActive)
        {
            // Calculate forces to nearest grid points
            float gridSpacing = interactionRadius * 0.5f;

            // Find nearest grid point
            float gridX = std::round(particle.x / gridSpacing) * gridSpacing;
            float gridY = std::round(particle.y / gridSpacing) * gridSpacing;

            // Calculate force towards grid point
            float dx = gridX - particle.x;
            float dy = gridY - particle.y;
            float distSq = dx * dx + dy * dy;

            if (distSq > 0.0001f)
            {
                float dist = std::sqrt(distSq);
                float forceX = dx / dist * behaviorIntensity * 0.5f;
                float forceY = dy / dist * behaviorIntensity * 0.5f;

                particle.applyForce(forceX, forceY);
            }

            // Add repulsion from other particles at same grid point
            for (auto& other : particles)
            {
                if (other.isActive && &other != &particle)
                {
                    float otherGridX = std::round(other.x / gridSpacing) * gridSpacing;
                    float otherGridY = std::round(other.y / gridSpacing) * gridSpacing;

                    if (std::abs(gridX - otherGridX) < 0.1f && std::abs(gridY - otherGridY) < 0.1f)
                    {
                        float dx = particle.x - other.x;
                        float dy = particle.y - other.y;
                        float distSq = dx * dx + dy * dy;

                        if (distSq > 0.0001f && distSq < gridSpacing * gridSpacing)
                        {
                            float dist = std::sqrt(distSq);
                            float repelForce = (gridSpacing - dist) / gridSpacing * behaviorIntensity * 0.2f;

                            particle.applyForce(dx / dist * repelForce, dy / dist * repelForce);
                        }
                    }
                }
            }
        }
    }
}

void ParticleSystem::applyWaveBehavior(float deltaTime)
{
    // Wave-like behavior with oscillating vertical positions
    float time = static_cast<float>(juce::Time::getMillisecondCounterHiRes() / 1000.0);

    for (auto& particle : particles)
    {
        if (particle.isActive)
        {
            // Calculate wave parameters based on x position
            float frequency = 1.0f + particle.x * 0.01f;
            float amplitude = 10.0f * behaviorIntensity;
            float phase = particle.x * 0.05f;

            // Calculate target y position based on sine wave
            float targetY = amplitude * std::sin(frequency * time + phase);

            // Apply force towards target y
            float forceY = (targetY - particle.y) * 0.1f;

            // Add small horizontal force for movement
            float forceX = std::sin(time * 0.5f + particle.y * 0.01f) * behaviorIntensity * 0.2f;

            particle.applyForce(forceX, forceY);
        }
    }
}

int ParticleSystem::findInactiveParticle()
{
    for (int i = 0; i < particles.size(); ++i)
    {
        if (!particles[i].isActive)
        {
            return i;
        }
    }

    // If all particles are active, return -1
    return -1;
}

void ParticleSystem::processGrain(Particle& particle, juce::AudioBuffer<float>& outputBuffer, const juce::AudioBuffer<float>& inputBuffer)
{
    // This is a placeholder implementation
    // In a real implementation, we would:
    // 1. Calculate the grain window based on particle's position in its lifecycle
    // 2. Apply the envelope
    // 3. Apply pitch shifting
    // 4. Apply panning
    // 5. Mix the grain into the output buffer

    // For now, just add a simple sine wave based on particle position
    float frequency = 220.0f * std::pow(2.0f, particle.pitchShift / 12.0f);
    float amplitude = 0.1f * (1.0f - particle.getNormalizedAge());

    for (int channel = 0; channel < outputBuffer.getNumChannels(); ++channel)
    {
        float channelGain = 1.0f;

        // Apply panning
        if (channel == 0) // Left channel
            channelGain = 0.5f * (1.0f - particle.pan);
        else // Right channel
            channelGain = 0.5f * (1.0f + particle.pan);

        float* channelData = outputBuffer.getWritePointer(channel);

        for (int sample = 0; sample < outputBuffer.getNumSamples(); ++sample)
        {
            float time = static_cast<float>(particle.bufferPosition + sample) / static_cast<float>(sampleRate);
            float sineValue = std::sin(2.0f * juce::MathConstants<float>::pi * frequency * time);

            // Apply envelope
            float envelopeValue = applyEnvelope(particle, static_cast<float>(sample) / static_cast<float>(outputBuffer.getNumSamples()));

            channelData[sample] += sineValue * amplitude * channelGain * envelopeValue;
        }
    }

    // Update buffer position
    particle.bufferPosition += outputBuffer.getNumSamples();
}

float ParticleSystem::applyEnvelope(const Particle& particle, float normalizedPosition)
{
    // Apply different envelope shapes based on particle's envelope type
    switch (particle.envelopeType)
    {
        case 0: // Gaussian
        {
            float x = (normalizedPosition - 0.5f) * 2.0f;
            return std::exp(-x * x * 4.0f);
        }

        case 1: // Triangle
        {
            return 1.0f - std::abs(normalizedPosition * 2.0f - 1.0f);
        }

        case 2: // Rectangle
        {
            return 1.0f;
        }

        case 3: // Cosine
        {
            return 0.5f * (1.0f - std::cos(normalizedPosition * juce::MathConstants<float>::twoPi));
        }

        default:
            return 1.0f;
    }
}

void ParticleSystem::setBoundaries(float left, float right, float top, float bottom)
{
    boundaryLeft = left;
    boundaryRight = right;
    boundaryTop = top;
    boundaryBottom = bottom;
}

void ParticleSystem::setBoundaryBehavior(BoundaryBehavior behavior)
{
    boundaryBehavior = behavior;
}

void ParticleSystem::addForceField(std::unique_ptr<ForceField> field)
{
    forceFields.push_back(std::move(field));
}

void ParticleSystem::clearForceFields()
{
    forceFields.clear();
}

void ParticleSystem::applyForceFields(float deltaTime)
{
    for (auto& field : forceFields)
    {
        if (field->isActive())
        {
            for (auto& particle : particles)
            {
                if (particle.isActive)
                {
                    field->applyToParticle(particle, deltaTime);
                }
            }
        }
    }
}

void ParticleSystem::handleCollisions()
{
    // Use spatial partitioning grid for efficient collision detection
    for (int gridX = 0; gridX < GRID_SIZE; ++gridX)
    {
        for (int gridY = 0; gridY < GRID_SIZE; ++gridY)
        {
            const auto& cell = grid[gridX][gridY];

            // Check collisions within this cell
            for (size_t i = 0; i < cell.particleIndices.size(); ++i)
            {
                Particle& particle1 = particles[cell.particleIndices[i]];

                if (!particle1.isActive)
                    continue;

                // Check against other particles in same cell
                for (size_t j = i + 1; j < cell.particleIndices.size(); ++j)
                {
                    Particle& particle2 = particles[cell.particleIndices[j]];

                    if (particle2.isActive && particle1.isCollidingWith(particle2))
                    {
                        particle1.handleCollision(particle2);
                    }
                }

                // Check against neighboring cells
                for (int nx = juce::jmax(0, gridX - 1); nx <= juce::jmin(GRID_SIZE - 1, gridX + 1); ++nx)
                {
                    for (int ny = juce::jmax(0, gridY - 1); ny <= juce::jmin(GRID_SIZE - 1, gridY + 1); ++ny)
                    {
                        // Skip same cell (already processed)
                        if (nx == gridX && ny == gridY)
                            continue;

                        const auto& neighborCell = grid[nx][ny];

                        for (int neighborIndex : neighborCell.particleIndices)
                        {
                            Particle& particle2 = particles[neighborIndex];

                            if (particle2.isActive && particle1.isCollidingWith(particle2))
                            {
                                particle1.handleCollision(particle2);
                            }
                        }
                    }
                }
            }
        }
    }
}

void ParticleSystem::handleBoundaries()
{
    for (auto& particle : particles)
    {
        if (!particle.isActive)
            continue;

        switch (boundaryBehavior)
        {
            case Wrap:
                // Wrap around boundaries
                if (particle.x < boundaryLeft)
                    particle.x = boundaryRight - (boundaryLeft - particle.x);
                else if (particle.x > boundaryRight)
                    particle.x = boundaryLeft + (particle.x - boundaryRight);

                if (particle.y < boundaryTop)
                    particle.y = boundaryBottom - (boundaryTop - particle.y);
                else if (particle.y > boundaryBottom)
                    particle.y = boundaryTop + (particle.y - boundaryBottom);
                break;

            case Bounce:
                // Bounce off boundaries
                particle.handleBoundaryCollision(boundaryLeft, boundaryRight, boundaryTop, boundaryBottom);
                break;

            case Absorb:
                // Deactivate particles that hit boundaries
                if (particle.x < boundaryLeft || particle.x > boundaryRight ||
                    particle.y < boundaryTop || particle.y > boundaryBottom)
                {
                    particle.isActive = false;
                }
                break;

            case Reflect:
                // Reflect off boundaries (similar to bounce but with perfect reflection)
                if (particle.x < boundaryLeft)
                {
                    particle.x = boundaryLeft + (boundaryLeft - particle.x);
                    particle.vx = -particle.vx;
                }
                else if (particle.x > boundaryRight)
                {
                    particle.x = boundaryRight - (particle.x - boundaryRight);
                    particle.vx = -particle.vx;
                }

                if (particle.y < boundaryTop)
                {
                    particle.y = boundaryTop + (boundaryTop - particle.y);
                    particle.vy = -particle.vy;
                }
                else if (particle.y > boundaryBottom)
                {
                    particle.y = boundaryBottom - (particle.y - boundaryBottom);
                    particle.vy = -particle.vy;
                }
                break;
        }
    }
}

void ParticleSystem::updateGrid()
{
    // Clear grid
    for (auto& row : grid)
    {
        for (auto& cell : row)
        {
            cell.particleIndices.clear();
        }
    }

    // Add particles to grid
    for (int i = 0; i < particles.size(); ++i)
    {
        const auto& particle = particles[i];

        if (particle.isActive)
        {
            int gridX, gridY;
            getGridCell(particle.x, particle.y, gridX, gridY);

            // Add particle index to grid cell
            if (gridX >= 0 && gridX < GRID_SIZE && gridY >= 0 && gridY < GRID_SIZE)
            {
                grid[gridX][gridY].particleIndices.push_back(i);
            }
        }
    }
}

void ParticleSystem::getGridCell(float x, float y, int& gridX, int& gridY)
{
    // Map world coordinates to grid coordinates
    float normalizedX = (x - boundaryLeft) / (boundaryRight - boundaryLeft);
    float normalizedY = (y - boundaryTop) / (boundaryBottom - boundaryTop);

    gridX = static_cast<int>(normalizedX * GRID_SIZE);
    gridY = static_cast<int>(normalizedY * GRID_SIZE);

    // Clamp to grid bounds
    gridX = juce::jlimit(0, GRID_SIZE - 1, gridX);
    gridY = juce::jlimit(0, GRID_SIZE - 1, gridY);
}

void ParticleSystem::applyAudioAnalysis(const juce::AudioBuffer<float>& buffer)
{
    // Perform FFT analysis
    performFFTAnalysis(buffer);

    // Apply FFT data to particles
    int activeCount = getActiveParticleCount();
    if (activeCount == 0)
        return;

    // Calculate frequency bands (simple version)
    float lowBand = 0.0f;
    float midBand = 0.0f;
    float highBand = 0.0f;

    // Low band (0-200Hz): bins 0-9 for 44.1kHz
    for (int i = 1; i < 10; ++i)
    {
        lowBand += fftMagnitudes[i];
    }
    lowBand /= 9.0f;

    // Mid band (200Hz-2kHz): bins 10-99
    for (int i = 10; i < 100; ++i)
    {
        midBand += fftMagnitudes[i];
    }
    midBand /= 90.0f;

    // High band (2kHz-20kHz): bins 100-999
    for (int i = 100; i < 1000; ++i)
    {
        highBand += fftMagnitudes[i];
    }
    highBand /= 900.0f;

    // Normalize bands
    float maxBand = juce::jmax(lowBand, midBand, highBand);
    if (maxBand > 0.0f)
    {
        lowBand /= maxBand;
        midBand /= maxBand;
        highBand /= maxBand;
    }

    // Apply to particles with smooth transitions to avoid sudden changes
    for (auto& particle : particles)
    {
        if (particle.isActive)
        {
            // Low frequencies affect particle size/mass (with smoothing)
            float targetMass = 1.0f + lowBand * 1.0f; // 减少影响强度
            particle.mass = particle.mass * 0.9f + targetMass * 0.1f; // 平滑过渡

            float targetRadius = 5.0f + lowBand * 5.0f; // 减少变化范围
            particle.radius = particle.radius * 0.9f + targetRadius * 0.1f;

            // Mid frequencies affect particle velocity (with limits and smoothing)
            float speedFactor = 1.0f + midBand * 0.5f; // 大幅减少速度影响
            speedFactor = juce::jlimit(0.8f, 1.2f, speedFactor); // 限制速度变化范围

            particle.vx = particle.vx * 0.95f + (particle.vx * speedFactor) * 0.05f; // 非常平滑的过渡
            particle.vy = particle.vy * 0.95f + (particle.vy * speedFactor) * 0.05f;

            // High frequencies affect particle energy and state (with smoothing)
            float targetEnergy = juce::jmax(0.1f, highBand * 1.0f); // 减少影响
            particle.energy = particle.energy * 0.8f + targetEnergy * 0.2f;

            // 减少状态变化的频率和强度
            if (highBand > 0.9f && juce::Random::getSystemRandom().nextFloat() < 0.02f) // 降低概率
            {
                particle.setState(ParticleState::Excited);
            }
        }
    }
}

void ParticleSystem::performFFTAnalysis(const juce::AudioBuffer<float>& buffer)
{
    // Only use first channel for analysis
    const float* channelData = buffer.getReadPointer(0);
    int numSamples = buffer.getNumSamples();

    // Fill FFT data (real part only, imaginary part is zero)
    for (int i = 0; i < juce::jmin(numSamples, 1024); ++i)
    {
        fftData[i * 2] = channelData[i];
        fftData[i * 2 + 1] = 0.0f;
    }

    // Zero-pad if buffer is smaller than FFT size
    for (int i = numSamples; i < 1024; ++i)
    {
        fftData[i * 2] = 0.0f;
        fftData[i * 2 + 1] = 0.0f;
    }

    // Apply window function (Hann window)
    for (int i = 0; i < 1024; ++i)
    {
        float windowValue = 0.5f * (1.0f - std::cos(2.0f * juce::MathConstants<float>::pi * i / 1023.0f));
        fftData[i * 2] *= windowValue;
    }

    // Perform FFT
    fft.performFrequencyOnlyForwardTransform(fftData.data());

    // Calculate magnitudes and apply some smoothing
    for (int i = 0; i < 1024; ++i)
    {
        float newMagnitude = fftData[i];
        fftMagnitudes[i] = fftMagnitudes[i] * 0.7f + newMagnitude * 0.3f;
    }
}

void ParticleSystem::getBoundaries(float& left, float& right, float& top, float& bottom) const
{
    left = boundaryLeft;
    right = boundaryRight;
    top = boundaryTop;
    bottom = boundaryBottom;
}

void ParticleSystem::handleUserInteraction(float x, float y, float strength, bool attract)
{
    // Create a temporary point force field
    PointForceField field(x, y, strength, attract);

    // Apply to all particles
    for (auto& particle : particles)
    {
        if (particle.isActive)
        {
            field.applyToParticle(particle, 0.016f); // Assuming ~60fps
        }
    }

    // Potentially emit new particles
    if (strength > 0.5f && juce::Random::getSystemRandom().nextFloat() < 0.2f)
    {
        // Emit 1-3 particles
        int count = juce::Random::getSystemRandom().nextInt(3) + 1;

        for (int i = 0; i < count; ++i)
        {
            float angle = juce::Random::getSystemRandom().nextFloat() * juce::MathConstants<float>::twoPi;
            float speed = 20.0f + juce::Random::getSystemRandom().nextFloat() * 30.0f;

            float vx = std::cos(angle) * speed;
            float vy = std::sin(angle) * speed;

            emitParticle(x, y, vx, vy);
        }
    }
}

void ParticleSystem::clear()
{
    for (auto& particle : particles)
    {
        particle.isActive = false;
        particle.age = 0.0f;
        particle.lifespan = 0.0f;
    }

    // Clear force fields
    clearForceFields();

    // Clear grid
    for (auto& row : grid)
    {
        for (auto& cell : row)
        {
            cell.particleIndices.clear();
        }
    }
}

// Performance-related method implementations
void ParticleSystem::setPerformanceTarget(float targetCpuUsage, float maxFrameTime)
{
    performanceMonitor.setTargetCpuUsage(targetCpuUsage);
    performanceMonitor.setMaxFrameTime(maxFrameTime);
}

void ParticleSystem::enableAdaptivePerformance(bool enabled)
{
    performanceMonitor.setAdaptiveMode(enabled);
}

size_t ParticleSystem::getMemoryUsage() const
{
    size_t totalMemory = 0;

    // Particle memory
    totalMemory += particles.size() * sizeof(Particle);

    // Grid memory
    totalMemory += sizeof(grid);

    // Audio buffer memory
    totalMemory += grainBuffer.getNumChannels() * grainBuffer.getNumSamples() * sizeof(float);

    // FFT data memory
    totalMemory += fftData.size() * sizeof(float);
    totalMemory += fftMagnitudes.size() * sizeof(float);

    // Performance monitor memory
    totalMemory += sizeof(performanceMonitor);

    return totalMemory;
}

void ParticleSystem::optimizeMemory()
{
    // Clear inactive particles
    for (auto& particle : particles)
    {
        if (!particle.isActive)
        {
            particle.reset();
        }
    }

    // Clear and rebuild spatial grid
    for (auto& row : grid)
    {
        for (auto& cell : row)
        {
            cell.particleIndices.clear();
        }
    }
    updateGrid();
}

void ParticleSystem::stabilizeParticleCount(int currentActiveCount)
{
    // 平滑当前活跃粒子数量
    lastActiveCount = lastActiveCount * (1.0f - particleCountSmoothingFactor) +
                      currentActiveCount * particleCountSmoothingFactor;

    // 计算与目标数量的差异
    float countDifference = targetParticleCount - lastActiveCount;

    // 如果粒子数量过少，尝试激活一些粒子
    if (countDifference > 5.0f && juce::Random::getSystemRandom().nextFloat() < 0.3f)
    {
        // 找到一个非活跃粒子并激活它
        for (auto& particle : particles)
        {
            if (!particle.isActive)
            {
                // 在中心附近随机位置重新激活粒子
                float x = juce::Random::getSystemRandom().nextFloat() * 40.0f - 20.0f;
                float y = juce::Random::getSystemRandom().nextFloat() * 40.0f - 20.0f;
                float vx = (juce::Random::getSystemRandom().nextFloat() - 0.5f) * 20.0f;
                float vy = (juce::Random::getSystemRandom().nextFloat() - 0.5f) * 20.0f;

                particle.reset();
                particle.x = x;
                particle.y = y;
                particle.vx = vx;
                particle.vy = vy;
                particle.isActive = true;
                particle.lifespan = 5.0f + juce::Random::getSystemRandom().nextFloat() * 10.0f;
                particle.age = 0.0f;

                break; // 一次只激活一个粒子，保持平滑
            }
        }
    }

    // 如果粒子数量过多，减少一些粒子的生命周期
    else if (countDifference < -10.0f && juce::Random::getSystemRandom().nextFloat() < 0.1f)
    {
        // 找到生命周期较长的粒子并缩短其生命周期
        for (auto& particle : particles)
        {
            if (particle.isActive && particle.lifespan > 3.0f)
            {
                particle.lifespan *= 0.8f; // 缩短20%的生命周期
                break; // 一次只处理一个粒子
            }
        }
    }
}