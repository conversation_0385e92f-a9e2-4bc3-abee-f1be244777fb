/*
  ==============================================================================

    AudioBufferManager.h
    Created: 21 May 2023
    Author:  AuraBloom Team

    This file defines the AudioBufferManager class, which is responsible for
    managing the audio buffer used for granular synthesis.

  ==============================================================================
*/

#pragma once

#include <JuceHeader.h>

/**
 * Manages the audio buffer used for granular synthesis.
 *
 * This class handles storing input audio in a circular buffer,
 * and provides methods for reading from the buffer with various
 * playback strategies (forward, backward, random, etc.).
 */
class AudioBufferManager
{
public:
    /**
     * Constructor.
     */
    AudioBufferManager();

    /**
     * Destructor.
     */
    ~AudioBufferManager();

    /**
     * Initialize the buffer manager.
     *
     * @param numChannels Number of audio channels
     * @param maxLengthSeconds Maximum buffer length in seconds
     * @param sampleRate Sample rate in Hz
     */
    void initialize(int numChannels, float maxLengthSeconds, double sampleRate);

    /**
     * Add audio data to the buffer.
     *
     * @param inputBuffer The input audio buffer
     */
    void addAudioData(const juce::AudioBuffer<float>& inputBuffer);

    /**
     * Get the buffer size in samples.
     *
     * @return Buffer size in samples
     */
    int getBufferSize() const;

    /**
     * Get the buffer size in seconds.
     *
     * @return Buffer size in seconds
     */
    float getBufferSizeSeconds() const;

    /**
     * Get the current write position.
     *
     * @return Current write position in samples
     */
    int getWritePosition() const;

    /**
     * Get the normalized write position (0.0 to 1.0).
     *
     * @return Normalized write position
     */
    float getNormalizedWritePosition() const;

    /**
     * Set the playback position.
     *
     * @param normalizedPosition Normalized position (0.0 to 1.0)
     */
    void setPlaybackPosition(float normalizedPosition);

    /**
     * Set the scan speed.
     *
     * @param speed Scan speed (-200.0 to 200.0)
     */
    void setScanSpeed(float speed);

    /**
     * Set the loop mode.
     *
     * @param mode Loop mode (0: No Loop, 1: Forward, 2: Ping-Pong)
     */
    void setLoopMode(int mode);

    /**
     * Read a sample from the buffer.
     *
     * @param channel Audio channel
     * @param position Position in samples
     * @param direction Playback direction (1: forward, -1: backward, 0: random)
     * @return Sample value
     */
    float readSample(int channel, int position, int direction = 1) const;

    /**
     * Read a sample from the buffer using normalized position.
     *
     * @param channel Audio channel
     * @param normalizedPosition Normalized position (0.0 to 1.0)
     * @param direction Playback direction (1: forward, -1: backward, 0: random)
     * @return Sample value
     */
    float readSampleNormalized(int channel, float normalizedPosition, int direction = 1) const;

    /**
     * Update the playback position based on scan speed and loop mode.
     *
     * @param deltaTime Time elapsed since last update in seconds
     */
    void updatePlaybackPosition(float deltaTime);

    /**
     * Get the current playback position.
     *
     * @return Current playback position in samples
     */
    int getPlaybackPosition() const;

    /**
     * Get the normalized playback position (0.0 to 1.0).
     *
     * @return Normalized playback position
     */
    float getNormalizedPlaybackPosition() const;

    /**
     * Get the buffer.
     *
     * @return Reference to the audio buffer
     */
    const juce::AudioBuffer<float>& getBuffer() const;

    /**
     * Clear the buffer and reset positions.
     */
    void clear();

private:
    juce::AudioBuffer<float> buffer;
    int writePosition = 0;
    int playbackPosition = 0;
    float scanSpeed = 0.0f;
    int loopMode = 0; // 0: No Loop, 1: Forward, 2: Ping-Pong
    double sampleRate = 44100.0;
    bool pingPongForward = true; // Direction for ping-pong mode

    /**
     * Convert normalized position (0.0 to 1.0) to sample position.
     *
     * @param normalizedPosition Normalized position
     * @return Sample position
     */
    int normalizedToSamplePosition(float normalizedPosition) const;

    /**
     * Convert sample position to normalized position (0.0 to 1.0).
     *
     * @param samplePosition Sample position
     * @return Normalized position
     */
    float sampleToNormalizedPosition(int samplePosition) const;

    /**
     * Wrap a sample position to the valid range.
     *
     * @param position Sample position
     * @return Wrapped position
     */
    int wrapPosition(int position) const;

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(AudioBufferManager)
};
