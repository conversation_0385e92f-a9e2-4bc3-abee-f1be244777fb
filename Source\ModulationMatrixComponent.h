/*
  ==============================================================================

    ModulationMatrixComponent.h
    Created: 21 May 2023
    Author:  AuraBloom Team

    This file defines the ModulationMatrixComponent class, which is used to
    display and control the modulation matrix.

  ==============================================================================
*/

#pragma once

#include <JuceHeader.h>
#include "ModulationMatrix.h"

/**
 * Component for displaying and controlling the modulation matrix.
 */
class ModulationMatrixComponent : public juce::Component,
                                 private juce::TableListBoxModel,
                                 private juce::Timer
{
public:
    /**
     * Constructor.
     *
     * @param matrix The modulation matrix to display and control
     */
    ModulationMatrixComponent(ModulationMatrix& matrix);

    /**
     * Destructor.
     */
    ~ModulationMatrixComponent() override;

    /**
     * Paint the component.
     */
    void paint(juce::Graphics& g) override;

    /**
     * Handle component resize.
     */
    void resized() override;

    /**
     * Get the number of rows in the table.
     */
    int getNumRows() override;

    /**
     * Paint a row background.
     */
    void paintRowBackground(juce::Graphics& g, int rowNumber, int width, int height, bool rowIsSelected) override;

    /**
     * Paint a cell.
     */
    void paintCell(juce::Graphics& g, int rowNumber, int columnId, int width, int height, bool rowIsSelected) override;

    /**
     * Create a cell component.
     */
    juce::Component* refreshComponentForCell(int rowNumber, int columnId, bool isRowSelected, juce::Component* existingComponentToUpdate) override;

    /**
     * Handle cell click.
     */
    void cellClicked(int rowNumber, int columnId, const juce::MouseEvent& e) override;

    /**
     * Handle cell double click.
     */
    void cellDoubleClicked(int rowNumber, int columnId, const juce::MouseEvent& e) override;

    /**
     * Update connection amount.
     *
     * @param rowNumber The row number of the connection
     * @param amount The new amount
     */
    void updateConnectionAmount(int rowNumber, float amount);

    /**
     * Update connection enabled state.
     *
     * @param rowNumber The row number of the connection
     * @param enabled The new enabled state
     */
    void updateConnectionEnabled(int rowNumber, bool enabled);

    /**
     * Handle cell deletion.
     */
    void deleteKeyPressed(int lastRowSelected) override;

    /**
     * Handle return key press.
     */
    void returnKeyPressed(int lastRowSelected) override;

    /**
     * Handle selection change.
     */
    void selectedRowsChanged(int lastRowSelected) override;

    /**
     * Handle sort order change.
     */
    void sortOrderChanged(int newSortColumnId, bool isForwards) override;

private:
    // Reference to the modulation matrix
    ModulationMatrix& matrix;

    // UI Components
    juce::TableListBox table;
    juce::TextButton addButton;
    juce::TextButton removeButton;

    // Timer callback for animation
    void timerCallback() override;

    // Helper methods
    void updateTable();
    void addConnection();
    void removeConnection();

    // Internal state
    std::vector<ModulationConnection> connections;

    enum ColumnIds
    {
        SourceColumn = 1,
        TargetColumn,
        AmountColumn,
        EnabledColumn
    };

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(ModulationMatrixComponent)
};
