/*
  ==============================================================================

    AudioBufferManager.cpp
    Created: 21 May 2023
    Author:  AuraBloom Team

    Implementation of the AudioBufferManager class.

  ==============================================================================
*/

#include "AudioBufferManager.h"

AudioBufferManager::AudioBufferManager()
{
}

AudioBufferManager::~AudioBufferManager()
{
}

void AudioBufferManager::initialize(int numChannels, float maxLengthSeconds, double sampleRate)
{
    this->sampleRate = sampleRate;
    int bufferSize = static_cast<int>(maxLengthSeconds * sampleRate);

    buffer.setSize(numChannels, bufferSize);
    buffer.clear();

    writePosition = 0;
    playbackPosition = 0;
}

void AudioBufferManager::addAudioData(const juce::AudioBuffer<float>& inputBuffer)
{
    const int numChannels = juce::jmin(buffer.getNumChannels(), inputBuffer.getNumChannels());
    const int numSamples = inputBuffer.getNumSamples();

    // Copy input data to the circular buffer
    for (int channel = 0; channel < numChannels; ++channel)
    {
        const float* inputData = inputBuffer.getReadPointer(channel);
        float* bufferData = buffer.getWritePointer(channel);

        for (int sample = 0; sample < numSamples; ++sample)
        {
            int position = (writePosition + sample) % buffer.getNumSamples();
            bufferData[position] = inputData[sample];
        }
    }

    // Update write position
    writePosition = (writePosition + numSamples) % buffer.getNumSamples();
}

int AudioBufferManager::getBufferSize() const
{
    return buffer.getNumSamples();
}

float AudioBufferManager::getBufferSizeSeconds() const
{
    return static_cast<float>(buffer.getNumSamples()) / static_cast<float>(sampleRate);
}

int AudioBufferManager::getWritePosition() const
{
    return writePosition;
}

float AudioBufferManager::getNormalizedWritePosition() const
{
    return sampleToNormalizedPosition(writePosition);
}

void AudioBufferManager::setPlaybackPosition(float normalizedPosition)
{
    playbackPosition = normalizedToSamplePosition(normalizedPosition);
}

void AudioBufferManager::setScanSpeed(float speed)
{
    // Clamp speed to -200.0 to 200.0
    scanSpeed = juce::jlimit(-200.0f, 200.0f, speed);
}

void AudioBufferManager::setLoopMode(int mode)
{
    // Clamp mode to 0-2
    loopMode = juce::jlimit(0, 2, mode);
}

float AudioBufferManager::readSample(int channel, int position, int direction) const
{
    // Safety checks
    if (buffer.getNumSamples() == 0 || buffer.getNumChannels() == 0)
        return 0.0f;

    // Clamp channel with extra safety
    if (channel < 0 || channel >= buffer.getNumChannels())
        channel = 0;

    // Handle playback direction with extra bounds checking
    int actualPosition;

    try {
        if (direction == 1) // Forward
        {
            actualPosition = wrapPosition(position);
        }
        else if (direction == -1) // Backward
        {
            // Ensure we don't go out of bounds
            if (position > buffer.getNumSamples() * 2)
                position = buffer.getNumSamples() - 1;

            actualPosition = wrapPosition(buffer.getNumSamples() - 1 - position);
        }
        else // Random or other
        {
            // For random, we'll just use the position as is, but with bounds checking
            actualPosition = wrapPosition(position);
        }

        // Final safety check
        if (actualPosition < 0 || actualPosition >= buffer.getNumSamples())
            return 0.0f;

        // Get sample with bounds checking
        return buffer.getSample(channel, actualPosition);
    }
    catch (...) {
        // If any exception occurs, return silence
        return 0.0f;
    }
}

float AudioBufferManager::readSampleNormalized(int channel, float normalizedPosition, int direction) const
{
    int position = normalizedToSamplePosition(normalizedPosition);
    return readSample(channel, position, direction);
}

void AudioBufferManager::updatePlaybackPosition(float deltaTime)
{
    // Calculate position change based on scan speed
    float positionChange = scanSpeed * deltaTime * static_cast<float>(sampleRate) / 100.0f;

    // Update position based on loop mode
    switch (loopMode)
    {
        case 0: // No Loop
        {
            playbackPosition += static_cast<int>(positionChange);

            // Clamp to buffer bounds
            playbackPosition = juce::jlimit(0, buffer.getNumSamples() - 1, playbackPosition);
            break;
        }

        case 1: // Forward Loop
        {
            playbackPosition += static_cast<int>(positionChange);
            playbackPosition = wrapPosition(playbackPosition);
            break;
        }

        case 2: // Ping-Pong Loop
        {
            if (pingPongForward)
            {
                playbackPosition += static_cast<int>(positionChange);

                if (playbackPosition >= buffer.getNumSamples())
                {
                    playbackPosition = 2 * buffer.getNumSamples() - playbackPosition - 1;
                    pingPongForward = false;
                }
            }
            else
            {
                playbackPosition -= static_cast<int>(positionChange);

                if (playbackPosition < 0)
                {
                    playbackPosition = -playbackPosition;
                    pingPongForward = true;
                }
            }

            // Ensure position is within bounds
            playbackPosition = juce::jlimit(0, buffer.getNumSamples() - 1, playbackPosition);
            break;
        }
    }
}

int AudioBufferManager::getPlaybackPosition() const
{
    return playbackPosition;
}

float AudioBufferManager::getNormalizedPlaybackPosition() const
{
    return sampleToNormalizedPosition(playbackPosition);
}

const juce::AudioBuffer<float>& AudioBufferManager::getBuffer() const
{
    return buffer;
}

int AudioBufferManager::normalizedToSamplePosition(float normalizedPosition) const
{
    // Clamp normalized position to 0.0-1.0
    normalizedPosition = juce::jlimit(0.0f, 1.0f, normalizedPosition);

    // Convert to sample position
    return static_cast<int>(normalizedPosition * (buffer.getNumSamples() - 1));
}

float AudioBufferManager::sampleToNormalizedPosition(int samplePosition) const
{
    if (buffer.getNumSamples() <= 1)
        return 0.0f;

    // Clamp sample position
    samplePosition = juce::jlimit(0, buffer.getNumSamples() - 1, samplePosition);

    // Convert to normalized position
    return static_cast<float>(samplePosition) / static_cast<float>(buffer.getNumSamples() - 1);
}

int AudioBufferManager::wrapPosition(int position) const
{
    // Safety check for empty buffer
    if (buffer.getNumSamples() <= 1)
        return 0;

    const int bufferSize = buffer.getNumSamples();

    // More efficient wrapping for negative values
    if (position < 0) {
        // Calculate how many buffer lengths we need to add
        int offset = ((-position) / bufferSize) + 1;
        position += offset * bufferSize;
    }

    // Use modulo for positive values
    // This is more efficient than a while loop for large position values
    return position % bufferSize;
}

void AudioBufferManager::clear()
{
    buffer.clear();
    writePosition = 0;
    playbackPosition = 0;
    pingPongForward = true;
}
