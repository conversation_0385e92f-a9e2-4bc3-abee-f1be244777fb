/*
  ==============================================================================

    ModulationSource.cpp
    Created: 21 May 2023
    Author:  AuraBloom Team

    Implementation of the ModulationSource class.

  ==============================================================================
*/

#include "ModulationSource.h"

ModulationSource::ModulationSource(const juce::String& name)
    : name(name)
{
}

ModulationSource::~ModulationSource()
{
}

const juce::String& ModulationSource::getName() const
{
    return name;
}

void ModulationSource::setName(const juce::String& name)
{
    this->name = name;
}

float ModulationSource::getValue() const
{
    return value;
}

bool ModulationSource::isBipolar() const
{
    return bipolar;
}

void ModulationSource::setBipolar(bool bipolar)
{
    this->bipolar = bipolar;
}

bool ModulationSource::isActive() const
{
    return active;
}

void ModulationSource::setActive(bool active)
{
    this->active = active;
}

void ModulationSource::reset()
{
    value = 0.0f;
    phase = 0.0f;
}

float ModulationSource::getValueAt(float timeOffset) const
{
    // Base implementation just returns the current value
    // Derived classes should override this to provide time-based values
    return value;
}

float ModulationSource::getDepth() const
{
    return depth;
}

void ModulationSource::setDepth(float depth)
{
    this->depth = juce::jlimit(0.0f, 1.0f, depth);
}

float ModulationSource::getPhase() const
{
    return phase;
}

void ModulationSource::setPhase(float phase)
{
    this->phase = phase - std::floor(phase); // Wrap to 0.0-1.0
}

void ModulationSource::setValue(float newValue)
{
    // Store the raw value (typically -1.0 to 1.0 for bipolar, 0.0 to 1.0 for unipolar)
    value = newValue * depth;
}

float ModulationSource::convertToUnipolarIfNeeded(float bipolarValue) const
{
    // If the source is unipolar, convert from bipolar (-1.0 to 1.0) to unipolar (0.0 to 1.0)
    if (!bipolar)
    {
        return (bipolarValue + 1.0f) * 0.5f;
    }
    
    return bipolarValue;
}
