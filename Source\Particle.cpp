/*
  ==============================================================================

    Particle.cpp
    Created: 21 May 2023
    Author:  AuraBloom Team

    Implementation of the Particle class.

  ==============================================================================
*/

#include "Particle.h"
#include "ParticleSystem.h"

Particle::Particle()
{
    // Initialize with default values
    isActive = false;
    state = ParticleState::Inactive;
    type = ParticleType::Normal;

    // Initialize position history
    for (auto& point : positionHistory)
    {
        point.setXY(0.0f, 0.0f);
    }
}

Particle::~Particle()
{
}

void Particle::update(float deltaTime)
{
    if (!isActive)
        return;

    // Update age
    age += deltaTime;

    // Apply acceleration to velocity
    vx += ax * deltaTime;
    vy += ay * deltaTime;

    // Reset acceleration
    ax = 0.0f;
    ay = 0.0f;

    // Update position based on velocity
    x += vx * deltaTime;
    y += vy * deltaTime;

    // Update history
    updateHistory();

    // Update state and type specific behaviors
    updateStateEffects(deltaTime);
    updateTypeEffects(deltaTime);

    // Update buffer position
    updateBufferPosition(deltaTime);

    // Decay energy over time
    energy = juce::jmax(0.0f, energy - 0.05f * deltaTime);

    // Check if particle should transition to decaying state
    if (state == ParticleState::Active && getNormalizedAge() > 0.8f)
    {
        setState(ParticleState::Decaying);
    }
}

bool Particle::isAlive() const
{
    return isActive && age < lifespan;
}

float Particle::getNormalizedAge() const
{
    return juce::jlimit(0.0f, 1.0f, age / lifespan);
}

void Particle::applyForce(float fx, float fy)
{
    // Apply force to acceleration
    ax += fx;
    ay += fy;
}

void Particle::applyForceWithMass(float fx, float fy)
{
    // Apply force to acceleration, considering mass (F = ma, so a = F/m)
    ax += fx / mass;
    ay += fy / mass;
}

void Particle::setState(ParticleState newState)
{
    // Only process if state is changing
    if (state == newState)
        return;

    // Handle state transition effects
    switch (newState)
    {
        case ParticleState::Active:
            isActive = true;
            energy = 1.0f;
            break;

        case ParticleState::Excited:
            energy = 1.5f;
            // Increase velocity when excited
            vx *= 1.5f;
            vy *= 1.5f;
            break;

        case ParticleState::Decaying:
            energy = 0.7f;
            // Slow down when decaying
            vx *= 0.7f;
            vy *= 0.7f;
            break;

        case ParticleState::Transforming:
            // Random velocity change during transformation
            vx += juce::Random::getSystemRandom().nextFloat() * 20.0f - 10.0f;
            vy += juce::Random::getSystemRandom().nextFloat() * 20.0f - 10.0f;
            break;

        case ParticleState::Inactive:
            isActive = false;
            break;
    }

    state = newState;
}

void Particle::setType(ParticleType newType)
{
    // Only process if type is changing
    if (type == newType)
        return;

    // Handle type transition effects
    switch (newType)
    {
        case ParticleType::Attractor:
            charge = 1.0f;
            mass = 2.0f;
            radius = 8.0f;
            break;

        case ParticleType::Repeller:
            charge = -1.0f;
            mass = 1.5f;
            radius = 7.0f;
            break;

        case ParticleType::Oscillator:
            // Oscillators have periodic behavior
            energy = 1.2f;
            break;

        case ParticleType::Catalyst:
            // Catalysts affect other particles
            energy = 1.5f;
            radius = 10.0f;
            break;

        case ParticleType::Normal:
        default:
            charge = 0.0f;
            mass = 1.0f;
            radius = 5.0f;
            break;
    }

    type = newType;
}

float Particle::distanceTo(const Particle& other) const
{
    float dx = other.x - x;
    float dy = other.y - y;
    return std::sqrt(dx * dx + dy * dy);
}

bool Particle::isCollidingWith(const Particle& other) const
{
    return distanceTo(other) < (radius + other.radius);
}

void Particle::handleCollision(Particle& other)
{
    // Calculate distance and normal vector
    float dx = other.x - x;
    float dy = other.y - y;
    float distance = std::sqrt(dx * dx + dy * dy);

    // Avoid division by zero
    if (distance < 0.001f)
        return;

    // Calculate normal vector
    float nx = dx / distance;
    float ny = dy / distance;

    // Calculate relative velocity
    float rvx = other.vx - vx;
    float rvy = other.vy - vy;

    // Calculate relative velocity along normal
    float velAlongNormal = rvx * nx + rvy * ny;

    // Do not resolve if velocities are separating
    if (velAlongNormal > 0)
        return;

    // Calculate restitution (bounciness)
    float restitution = 0.8f;

    // Calculate impulse scalar
    float j = -(1.0f + restitution) * velAlongNormal;
    j /= 1.0f / mass + 1.0f / other.mass;

    // Apply impulse
    float impulsex = j * nx;
    float impulsey = j * ny;

    // Apply impulse to velocities
    vx -= impulsex / mass;
    vy -= impulsey / mass;
    other.vx += impulsex / other.mass;
    other.vy += impulsey / other.mass;

    // Handle energy transfer
    if (energy > other.energy)
    {
        float transfer = (energy - other.energy) * 0.1f;
        energy -= transfer;
        other.energy += transfer;
    }
    else
    {
        float transfer = (other.energy - energy) * 0.1f;
        energy += transfer;
        other.energy -= transfer;
    }

    // Handle state changes based on collision
    if (state == ParticleState::Excited && other.state == ParticleState::Active)
    {
        other.setState(ParticleState::Excited);
    }
    else if (type == ParticleType::Catalyst && other.state == ParticleState::Active)
    {
        other.setState(ParticleState::Transforming);
    }
}

void Particle::handleBoundaryCollision(float left, float right, float top, float bottom, float elasticity)
{
    // Check and handle collision with left/right boundaries
    if (x - radius < left)
    {
        x = left + radius;
        vx = -vx * elasticity;
    }
    else if (x + radius > right)
    {
        x = right - radius;
        vx = -vx * elasticity;
    }

    // Check and handle collision with top/bottom boundaries
    if (y - radius < top)
    {
        y = top + radius;
        vy = -vy * elasticity;
    }
    else if (y + radius > bottom)
    {
        y = bottom - radius;
        vy = -vy * elasticity;
    }
}

void Particle::updateHistory()
{
    // Add current position to history
    positionHistory[historyIndex].setXY(x, y);

    // Update history index
    historyIndex = (historyIndex + 1) % HISTORY_SIZE;
}

const std::array<juce::Point<float>, Particle::HISTORY_SIZE>& Particle::getPositionHistory() const
{
    return positionHistory;
}

void Particle::reset()
{
    // Reset all properties to default values
    x = 0.0f;
    y = 0.0f;
    vx = 0.0f;
    vy = 0.0f;
    ax = 0.0f;
    ay = 0.0f;
    mass = 1.0f;
    charge = 0.0f;
    energy = 1.0f;
    radius = 5.0f;
    grainSize = 50.0f;
    pitchShift = 0.0f;
    pan = 0.0f;
    playDirection = 0;
    envelopeType = 0;
    lifespan = 2.0f;
    age = 0.0f;
    isActive = false;
    state = ParticleState::Inactive;
    type = ParticleType::Normal;
    bufferPosition = 0;
    bufferPlaybackRate = 1.0f;

    // Reset history
    for (auto& point : positionHistory)
    {
        point.setXY(0.0f, 0.0f);
    }
    historyIndex = 0;
}

void Particle::emitChildParticles(ParticleSystem& system, int count)
{
    // Create child particles
    for (int i = 0; i < count; ++i)
    {
        // Get a new particle from the system
        Particle* child = system.createParticle();

        if (child != nullptr)
        {
            // Set child properties based on parent
            child->x = x;
            child->y = y;

            // Random velocity based on parent's velocity
            float angle = juce::Random::getSystemRandom().nextFloat() * juce::MathConstants<float>::twoPi;
            float speed = std::sqrt(vx * vx + vy * vy) * 0.5f + 10.0f;
            child->vx = std::cos(angle) * speed;
            child->vy = std::sin(angle) * speed;

            // Inherit some properties
            child->mass = mass * 0.5f;
            child->energy = energy * 0.7f;
            child->lifespan = lifespan * 0.5f;

            // Audio properties
            child->grainSize = grainSize * (0.8f + juce::Random::getSystemRandom().nextFloat() * 0.4f);
            child->pitchShift = pitchShift + juce::Random::getSystemRandom().nextFloat() * 4.0f - 2.0f;
            child->pan = juce::jlimit(-1.0f, 1.0f, pan + juce::Random::getSystemRandom().nextFloat() * 0.4f - 0.2f);

            // Activate the child
            child->isActive = true;
            child->setState(ParticleState::Active);
        }
    }
}

void Particle::updateBufferPosition(float deltaTime)
{
    // Update buffer position based on playback rate
    bufferPosition += static_cast<int>(bufferPlaybackRate * deltaTime * 44100.0f); // Assuming 44.1kHz sample rate
}

void Particle::updateStateEffects(float deltaTime)
{
    // Apply effects based on current state
    switch (state)
    {
        case ParticleState::Excited:
            // Excited particles have more erratic movement
            if (juce::Random::getSystemRandom().nextFloat() < 0.1f)
            {
                applyForce(juce::Random::getSystemRandom().nextFloat() * 20.0f - 10.0f,
                           juce::Random::getSystemRandom().nextFloat() * 20.0f - 10.0f);
            }
            break;

        case ParticleState::Decaying:
            // Decaying particles slow down
            vx *= 0.98f;
            vy *= 0.98f;
            break;

        case ParticleState::Transforming:
            // Transforming particles may change type
            if (juce::Random::getSystemRandom().nextFloat() < 0.05f)
            {
                int newTypeIndex = juce::Random::getSystemRandom().nextInt(5);
                setType(static_cast<ParticleType>(newTypeIndex));
                setState(ParticleState::Active);
            }
            break;

        default:
            break;
    }
}

void Particle::updateTypeEffects(float deltaTime)
{
    // Apply effects based on current type
    switch (type)
    {
        case ParticleType::Oscillator:
            // Oscillators have sinusoidal movement
            {
                float oscillation = std::sin(age * 5.0f) * 5.0f;
                applyForce(std::cos(age * 3.0f) * oscillation, std::sin(age * 3.0f) * oscillation);
            }
            break;

        case ParticleType::Catalyst:
            // Catalysts pulse with energy
            energy = 1.0f + 0.5f * std::sin(age * 8.0f);
            break;

        default:
            break;
    }
}
