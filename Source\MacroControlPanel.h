/*
  ==============================================================================

    MacroControlPanel.h
    Created: 21 May 2023
    Author:  AuraBloom Team

    宏控制面板 - 包含四个宏控制旋钮的面板

  ==============================================================================
*/

#pragma once

#include <JuceHeader.h>
#include "MacroControlKnob.h"

class MacroControlPanel : public juce::Component
{
public:
    MacroControlPanel();
    ~MacroControlPanel() override;

    // Component overrides
    void paint(juce::Graphics& g) override;
    void resized() override;

    // 宏控制访问
    MacroControlKnob& getGenesisKnob() { return genesisKnob; }
    MacroControlKnob& getScatterKnob() { return scatterKnob; }
    MacroControlKnob& getFormKnob() { return formKnob; }
    MacroControlKnob& getChaosKnob() { return chaosKnob; }

    // 值设置
    void setGenesisValue(float value) { genesisKnob.setValue(value); }
    void setScatterValue(float value) { scatterKnob.setValue(value); }
    void setFormValue(float value) { formKnob.setValue(value); }
    void setChaosValue(float value) { chaosKnob.setValue(value); }

    // 获取值
    float getGenesisValue() const { return genesisKnob.getValue(); }
    float getScatterValue() const { return scatterKnob.getValue(); }
    float getFormValue() const { return formKnob.getValue(); }
    float getChaosValue() const { return chaosKnob.getValue(); }

    // 回调函数设置
    void setParameterChangeCallback(std::function<void(const juce::String&, float)> callback);

    // 预设管理
    void savePreset(const juce::String& name);
    void loadPreset(const juce::String& name);
    std::vector<juce::String> getPresetNames() const;

private:
    // 宏控制旋钮
    MacroControlKnob genesisKnob;
    MacroControlKnob scatterKnob;
    MacroControlKnob formKnob;
    MacroControlKnob chaosKnob;

    // 预设浏览器
    juce::ComboBox presetComboBox;
    juce::TextButton savePresetButton;
    juce::Label presetLabel;

    // 预设数据
    struct MacroPreset
    {
        juce::String name;
        float genesisValue;
        float scatterValue;
        float formValue;
        float chaosValue;

        MacroPreset(const juce::String& n, float g, float s, float f, float c)
            : name(n), genesisValue(g), scatterValue(s), formValue(f), chaosValue(c) {}
    };

    std::vector<MacroPreset> presets;

    // 内部方法
    void setupKnobs();
    void setupPresetBrowser();
    void initializePresets();
    void updatePresetComboBox();
    void drawBackground(juce::Graphics& g);
    void drawTitle(juce::Graphics& g);

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(MacroControlPanel)
};
