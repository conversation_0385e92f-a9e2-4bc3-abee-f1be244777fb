/*
  ==============================================================================

    MacroControlPanel.h
    Created: 21 May 2023
    Author:  AuraBloom Team

    Macro Control Panel - Panel containing four macro control knobs

  ==============================================================================
*/

#pragma once

#include <JuceHeader.h>
#include "MacroControlKnob.h"

class MacroControlPanel : public juce::Component
{
public:
    MacroControlPanel();
    ~MacroControlPanel() override;

    // Component overrides
    void paint(juce::Graphics& g) override;
    void resized() override;

    // Macro control access
    MacroControlKnob& getGenesisKnob() { return genesisKnob; }
    MacroControlKnob& getScatterKnob() { return scatterKnob; }
    MacroControlKnob& getFormKnob() { return formKnob; }
    MacroControlKnob& getChaosKnob() { return chaosKnob; }

    // Value setting
    void setGenesisValue(float value) { genesisKnob.setValue(value); }
    void setScatterValue(float value) { scatterKnob.setValue(value); }
    void setFormValue(float value) { formKnob.setValue(value); }
    void setChaosValue(float value) { chaosKnob.setValue(value); }

    // Get values
    float getGenesisValue() const { return genesisKnob.getValue(); }
    float getScatterValue() const { return scatterKnob.getValue(); }
    float getFormValue() const { return formKnob.getValue(); }
    float getChaosValue() const { return chaosKnob.getValue(); }

    // Callback function setup
    void setParameterChangeCallback(std::function<void(const juce::String&, float)> callback);

    // Preset management
    void savePreset(const juce::String& name);
    void loadPreset(const juce::String& name);
    std::vector<juce::String> getPresetNames() const;

private:
    // Macro control knobs
    MacroControlKnob genesisKnob;
    MacroControlKnob scatterKnob;
    MacroControlKnob formKnob;
    MacroControlKnob chaosKnob;

    // Preset browser
    juce::ComboBox presetComboBox;
    juce::TextButton savePresetButton;
    juce::Label presetLabel;

    // Preset data
    struct MacroPreset
    {
        juce::String name;
        float genesisValue;
        float scatterValue;
        float formValue;
        float chaosValue;

        MacroPreset(const juce::String& n, float g, float s, float f, float c)
            : name(n), genesisValue(g), scatterValue(s), formValue(f), chaosValue(c) {}
    };

    std::vector<MacroPreset> presets;

    // Internal methods
    void setupKnobs();
    void setupPresetBrowser();
    void initializePresets();
    void updatePresetComboBox();
    void drawBackground(juce::Graphics& g);
    void drawTitle(juce::Graphics& g);

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(MacroControlPanel)
};
