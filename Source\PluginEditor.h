/*
  ==============================================================================

    This file contains the basic framework code for a JUCE plugin editor.

  ==============================================================================
*/

#pragma once

#include <JuceHeader.h>
#include "PluginProcessor.h"
#include "BloomChamberComponent.h"
#include "XYControlPad.h"
#include "MacroControlPanel.h"
#include "AuraBloomLookAndFeel.h"
#include "LFOComponent.h"
#include "EnvelopeComponent.h"
#include "ModulationMatrixComponent.h"

//==============================================================================
// Custom panel class for responsive layout
class ResponsivePanel : public juce::Component
{
public:
    ResponsivePanel() = default;

    void resized() override
    {
        if (layoutFunction)
            layoutFunction();
    }

    std::function<void()> layoutFunction;
};

//==============================================================================
/**
*/
class AuraBloomAudioProcessorEditor  : public juce::AudioProcessorEditor
{
public:
    AuraBloomAudioProcessorEditor (AuraBloomAudioProcessor&);
    ~AuraBloomAudioProcessorEditor() override;

    //==============================================================================
    void paint (juce::Graphics&) override;
    void resized() override;

private:
    // This reference is provided as a quick way for your editor to
    // access the processor object that created it.
    AuraBloomAudioProcessor& audioProcessor;

    // Look and feel
    AuraBloomLookAndFeel lookAndFeel;

    // UI Components
    BloomChamberComponent bloomChamber;
    MacroControlPanel macroControlPanel;

    // Tabs
    juce::TabbedComponent tabs { juce::TabbedButtonBar::TabsAtTop };

    // Modulation components
    std::unique_ptr<LFOComponent> lfo1Component;
    std::unique_ptr<LFOComponent> lfo2Component;
    std::unique_ptr<EnvelopeComponent> envelope1Component;
    std::unique_ptr<ModulationMatrixComponent> modulationMatrixComponent;

    // Tab panels
    std::unique_ptr<ResponsivePanel> emittersPanel;
    std::unique_ptr<ResponsivePanel> particlesPanel;
    std::unique_ptr<ResponsivePanel> modulationPanel;
    std::unique_ptr<ResponsivePanel> effectsPanel;

    // Emitter controls
    juce::Slider emitterRateSlider;
    juce::Label emitterRateLabel;
    juce::ComboBox emitterShapeCombo;
    juce::Label emitterShapeLabel;
    juce::ToggleButton emitterBurstToggle;
    juce::Label emitterBurstLabel;
    juce::Slider emitterVelocitySlider;
    juce::Label emitterVelocityLabel;
    juce::Slider emitterSpreadSlider;
    juce::Label emitterSpreadLabel;

    // Particle controls
    juce::Slider particleLifespanSlider;
    juce::Label particleLifespanLabel;
    juce::ComboBox particleBehaviorCombo;
    juce::Label particleBehaviorLabel;
    juce::Slider particleIntensitySlider;
    juce::Label particleIntensityLabel;
    juce::Slider particleRadiusSlider;
    juce::Label particleRadiusLabel;
    juce::Slider particleDampingSlider;
    juce::Label particleDampingLabel;

    // Stage 5: Advanced Collision controls (SAFE - NEW ADDITION)
    juce::ToggleButton collisionAdvancedToggle;
    juce::Label collisionAdvancedLabel;
    juce::Slider collisionElasticitySlider;
    juce::Label collisionElasticityLabel;
    juce::ToggleButton collisionAudioToggle;
    juce::Label collisionAudioLabel;

    // Effects controls
    juce::ComboBox filterTypeCombo;
    juce::Label filterTypeLabel;
    juce::Slider filterCutoffSlider;
    juce::Label filterCutoffLabel;
    juce::Slider filterResonanceSlider;
    juce::Label filterResonanceLabel;
    juce::Slider delayTimeSlider;
    juce::Label delayTimeLabel;
    juce::Slider delayFeedbackSlider;
    juce::Label delayFeedbackLabel;
    juce::Slider reverbSizeSlider;
    juce::Label reverbSizeLabel;

    // Macro knobs
    juce::Slider genesisKnob;
    juce::Slider scatterKnob;
    juce::Slider formKnob;
    juce::Slider driftKnob;
    juce::Slider flowKnob;
    juce::Slider evolveKnob;
    juce::Slider ambienceKnob;

    // Global controls
    juce::Slider mixSlider;
    juce::Slider gainSlider;

    // Labels
    juce::Label genesisLabel;
    juce::Label scatterLabel;
    juce::Label formLabel;
    juce::Label driftLabel;
    juce::Label flowLabel;
    juce::Label evolveLabel;
    juce::Label ambienceLabel;
    juce::Label mixLabel;
    juce::Label gainLabel;

    // Parameter attachments
    std::unique_ptr<juce::AudioProcessorValueTreeState::SliderAttachment> genesisAttachment;
    std::unique_ptr<juce::AudioProcessorValueTreeState::SliderAttachment> scatterAttachment;
    std::unique_ptr<juce::AudioProcessorValueTreeState::SliderAttachment> formAttachment;
    std::unique_ptr<juce::AudioProcessorValueTreeState::SliderAttachment> driftAttachment;

    // Emitter attachments
    std::unique_ptr<juce::AudioProcessorValueTreeState::SliderAttachment> emitterRateAttachment;
    std::unique_ptr<juce::AudioProcessorValueTreeState::ComboBoxAttachment> emitterShapeAttachment;
    std::unique_ptr<juce::AudioProcessorValueTreeState::ButtonAttachment> emitterBurstAttachment;
    std::unique_ptr<juce::AudioProcessorValueTreeState::SliderAttachment> emitterVelocityAttachment;
    std::unique_ptr<juce::AudioProcessorValueTreeState::SliderAttachment> emitterSpreadAttachment;

    // Particle attachments
    std::unique_ptr<juce::AudioProcessorValueTreeState::SliderAttachment> particleLifespanAttachment;
    std::unique_ptr<juce::AudioProcessorValueTreeState::ComboBoxAttachment> particleBehaviorAttachment;
    std::unique_ptr<juce::AudioProcessorValueTreeState::SliderAttachment> particleIntensityAttachment;
    std::unique_ptr<juce::AudioProcessorValueTreeState::SliderAttachment> particleRadiusAttachment;
    std::unique_ptr<juce::AudioProcessorValueTreeState::SliderAttachment> particleDampingAttachment;

    // Stage 5: Advanced Collision attachments (SAFE - NEW ADDITION)
    std::unique_ptr<juce::AudioProcessorValueTreeState::ButtonAttachment> collisionAdvancedAttachment;
    std::unique_ptr<juce::AudioProcessorValueTreeState::SliderAttachment> collisionElasticityAttachment;
    std::unique_ptr<juce::AudioProcessorValueTreeState::ButtonAttachment> collisionAudioAttachment;

    // Effects attachments
    std::unique_ptr<juce::AudioProcessorValueTreeState::ComboBoxAttachment> filterTypeAttachment;
    std::unique_ptr<juce::AudioProcessorValueTreeState::SliderAttachment> filterCutoffAttachment;
    std::unique_ptr<juce::AudioProcessorValueTreeState::SliderAttachment> filterResonanceAttachment;
    std::unique_ptr<juce::AudioProcessorValueTreeState::SliderAttachment> delayTimeAttachment;
    std::unique_ptr<juce::AudioProcessorValueTreeState::SliderAttachment> delayFeedbackAttachment;
    std::unique_ptr<juce::AudioProcessorValueTreeState::SliderAttachment> reverbSizeAttachment;
    std::unique_ptr<juce::AudioProcessorValueTreeState::SliderAttachment> flowAttachment;
    std::unique_ptr<juce::AudioProcessorValueTreeState::SliderAttachment> evolveAttachment;
    std::unique_ptr<juce::AudioProcessorValueTreeState::SliderAttachment> ambienceAttachment;
    std::unique_ptr<juce::AudioProcessorValueTreeState::SliderAttachment> mixAttachment;
    std::unique_ptr<juce::AudioProcessorValueTreeState::SliderAttachment> gainAttachment;

    // Helper methods
    void setupKnob(juce::Slider& knob, juce::Label& label, const juce::String& labelText);
    void setupSlider(juce::Slider& slider, juce::Label& label, const juce::String& labelText);
    void createTabPanels();
    void setupEmittersPanel();
    void setupParticlesPanel();
    void setupModulationPanel();
    void setupEffectsPanel();

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR (AuraBloomAudioProcessorEditor)
};
