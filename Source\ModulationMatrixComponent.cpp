/*
  ==============================================================================

    ModulationMatrixComponent.cpp
    Created: 21 May 2023
    Author:  AuraBloom Team

    Implementation of the ModulationMatrixComponent class.

  ==============================================================================
*/

#include "ModulationMatrixComponent.h"

// Custom cell component for amount slider
class AmountSliderComponent : public juce::Component
{
public:
    AmountSliderComponent(ModulationMatrixComponent& owner, int rowNumber)
        : owner(owner), rowNumber(rowNumber)
    {
        addAndMakeVisible(slider);
        slider.setRange(-1.0, 1.0, 0.01);
        slider.setSliderStyle(juce::Slider::LinearHorizontal);
        slider.setTextBoxStyle(juce::Slider::TextBoxRight, false, 50, 20);
        slider.setColour(juce::Slider::trackColourId, juce::Colour(60, 60, 100));
        slider.setColour(juce::Slider::thumbColourId, juce::Colour(100, 100, 255));
        slider.setColour(juce::Slider::textBoxTextColourId, juce::Colours::white);
        slider.setColour(juce::Slider::textBoxOutlineColourId, juce::Colours::transparentBlack);

        slider.onValueChange = [this, &owner, rowNumber] {
            // Update the connection amount without using MouseEvent
            // Just get the value and update the connection directly
            auto* amountSlider = dynamic_cast<AmountSliderComponent*>(this);
            if (amountSlider != nullptr) {
                float amount = amountSlider->getValue();

                // Get the connection
                if (rowNumber < owner.getNumRows()) {
                    // Update the connection in the matrix
                    owner.updateConnectionAmount(rowNumber, amount);
                }
            }
        };
    }

    void resized() override
    {
        slider.setBounds(getLocalBounds());
    }

    void setValue(float value)
    {
        slider.setValue(value, juce::dontSendNotification);
    }

    float getValue() const
    {
        return static_cast<float>(slider.getValue());
    }

private:
    ModulationMatrixComponent& owner;
    int rowNumber;
    juce::Slider slider;
};

// Custom cell component for enabled toggle
class EnabledToggleComponent : public juce::Component
{
public:
    EnabledToggleComponent(ModulationMatrixComponent& owner, int rowNumber)
        : owner(owner), rowNumber(rowNumber)
    {
        addAndMakeVisible(toggle);
        toggle.setColour(juce::ToggleButton::tickColourId, juce::Colour(100, 100, 255));
        toggle.setColour(juce::ToggleButton::tickDisabledColourId, juce::Colour(60, 60, 100));

        toggle.onClick = [this, &owner, rowNumber] {
            // Update the connection enabled state without using MouseEvent
            // Just get the value and update the connection directly
            auto* enabledToggle = dynamic_cast<EnabledToggleComponent*>(this);
            if (enabledToggle != nullptr) {
                bool enabled = enabledToggle->getValue();

                // Get the connection
                if (rowNumber < owner.getNumRows()) {
                    // Update the connection in the matrix
                    owner.updateConnectionEnabled(rowNumber, enabled);
                }
            }
        };
    }

    void resized() override
    {
        toggle.setBounds(getLocalBounds().reduced(4));
    }

    void setValue(bool value)
    {
        toggle.setToggleState(value, juce::dontSendNotification);
    }

    bool getValue() const
    {
        return toggle.getToggleState();
    }

private:
    ModulationMatrixComponent& owner;
    int rowNumber;
    juce::ToggleButton toggle;
};

ModulationMatrixComponent::ModulationMatrixComponent(ModulationMatrix& matrix)
    : matrix(matrix)
{
    // Set up table
    addAndMakeVisible(table);
    table.setModel(this);

    // Add columns
    table.getHeader().addColumn("Source", SourceColumn, 150);
    table.getHeader().addColumn("Target", TargetColumn, 150);
    table.getHeader().addColumn("Amount", AmountColumn, 150);
    table.getHeader().addColumn("Enabled", EnabledColumn, 80);

    // Set up table properties
    table.setColour(juce::ListBox::backgroundColourId, juce::Colour(30, 30, 60));
    table.setColour(juce::ListBox::outlineColourId, juce::Colour(60, 60, 100));
    table.setColour(juce::ListBox::textColourId, juce::Colours::white);
    table.setHeaderHeight(24);
    table.setRowHeight(30);
    table.setMultipleSelectionEnabled(false);

    // Set up add button
    addAndMakeVisible(addButton);
    addButton.setButtonText("Add");
    addButton.setColour(juce::TextButton::buttonColourId, juce::Colour(60, 60, 100));
    addButton.setColour(juce::TextButton::buttonOnColourId, juce::Colour(100, 100, 255));
    addButton.setColour(juce::TextButton::textColourOffId, juce::Colours::white);
    addButton.setColour(juce::TextButton::textColourOnId, juce::Colours::white);
    addButton.onClick = [this] { addConnection(); };

    // Set up remove button
    addAndMakeVisible(removeButton);
    removeButton.setButtonText("Remove");
    removeButton.setColour(juce::TextButton::buttonColourId, juce::Colour(60, 60, 100));
    removeButton.setColour(juce::TextButton::buttonOnColourId, juce::Colour(100, 100, 255));
    removeButton.setColour(juce::TextButton::textColourOffId, juce::Colours::white);
    removeButton.setColour(juce::TextButton::textColourOnId, juce::Colours::white);
    removeButton.onClick = [this] { removeConnection(); };

    // Update table
    updateTable();

    // Start timer for animation
    startTimerHz(10);
}

ModulationMatrixComponent::~ModulationMatrixComponent()
{
    // Stop timer
    stopTimer();
}

void ModulationMatrixComponent::paint(juce::Graphics& g)
{
    // Fill background
    g.fillAll(juce::Colour(40, 40, 80));

    // Draw border
    g.setColour(juce::Colour(60, 60, 100));
    g.drawRoundedRectangle(getLocalBounds().toFloat().reduced(1.0f), 4.0f, 2.0f);

    // Draw title
    g.setColour(juce::Colours::white);
    g.setFont(juce::FontOptions (16.0f));
    g.drawText("Modulation Matrix", 10, 10, 200, 20, juce::Justification::left, true);
}

void ModulationMatrixComponent::resized()
{
    auto bounds = getLocalBounds().reduced(10);

    // Title area
    bounds.removeFromTop(30);

    // Buttons area
    auto buttonsArea = bounds.removeFromBottom(30);
    auto addButtonArea = buttonsArea.removeFromLeft(buttonsArea.getWidth() / 2).reduced(5, 0);
    auto removeButtonArea = buttonsArea.reduced(5, 0);

    addButton.setBounds(addButtonArea);
    removeButton.setBounds(removeButtonArea);

    // Table area
    table.setBounds(bounds);
}

int ModulationMatrixComponent::getNumRows()
{
    return static_cast<int>(connections.size());
}

void ModulationMatrixComponent::paintRowBackground(juce::Graphics& g, int rowNumber, int width, int height, bool rowIsSelected)
{
    // Draw row background
    g.fillAll(rowIsSelected ? juce::Colour(60, 60, 100) : juce::Colour(40, 40, 80));
}

void ModulationMatrixComponent::paintCell(juce::Graphics& g, int rowNumber, int columnId, int width, int height, bool rowIsSelected)
{
    // Draw cell content
    g.setColour(juce::Colours::white);
    g.setFont(juce::FontOptions (14.0f));

    if (rowNumber < connections.size())
    {
        const auto& connection = connections[rowNumber];

        switch (columnId)
        {
            case SourceColumn:
                if (connection.source != nullptr)
                {
                    g.drawText(connection.source->getName(), 2, 0, width - 4, height, juce::Justification::centredLeft, true);
                }
                break;

            case TargetColumn:
                if (connection.target != nullptr)
                {
                    g.drawText(connection.target->getName(), 2, 0, width - 4, height, juce::Justification::centredLeft, true);
                }
                break;

            default:
                break;
        }
    }
}

juce::Component* ModulationMatrixComponent::refreshComponentForCell(int rowNumber, int columnId, bool isRowSelected, juce::Component* existingComponentToUpdate)
{
    // Create or update cell component
    if (rowNumber < connections.size())
    {
        const auto& connection = connections[rowNumber];

        switch (columnId)
        {
            case AmountColumn:
            {
                auto* amountSlider = static_cast<AmountSliderComponent*>(existingComponentToUpdate);

                if (amountSlider == nullptr)
                {
                    amountSlider = new AmountSliderComponent(*this, rowNumber);
                }

                amountSlider->setValue(connection.amount);
                return amountSlider;
            }

            case EnabledColumn:
            {
                auto* enabledToggle = static_cast<EnabledToggleComponent*>(existingComponentToUpdate);

                if (enabledToggle == nullptr)
                {
                    enabledToggle = new EnabledToggleComponent(*this, rowNumber);
                }

                enabledToggle->setValue(connection.enabled);
                return enabledToggle;
            }

            default:
                break;
        }
    }

    return nullptr;
}

void ModulationMatrixComponent::cellClicked(int rowNumber, int columnId, const juce::MouseEvent& e)
{
    // Handle cell click
    if (rowNumber < connections.size())
    {
        switch (columnId)
        {
            case AmountColumn:
            {
                auto* amountSlider = dynamic_cast<AmountSliderComponent*>(table.getCellComponent(columnId, rowNumber));

                if (amountSlider != nullptr)
                {
                    // Update connection amount
                    updateConnectionAmount(rowNumber, amountSlider->getValue());
                }
                break;
            }

            case EnabledColumn:
            {
                auto* enabledToggle = dynamic_cast<EnabledToggleComponent*>(table.getCellComponent(columnId, rowNumber));

                if (enabledToggle != nullptr)
                {
                    // Update connection enabled state
                    updateConnectionEnabled(rowNumber, enabledToggle->getValue());
                }
                break;
            }

            default:
                break;
        }
    }
}

void ModulationMatrixComponent::updateConnectionAmount(int rowNumber, float amount)
{
    if (rowNumber < connections.size())
    {
        auto& connection = connections[rowNumber];

        // Update connection in matrix
        matrix.removeConnection(connection.source, connection.target);
        matrix.createConnection(connection.source, connection.target, amount, connection.enabled);

        // Update local connection
        connection.amount = amount;
    }
}

void ModulationMatrixComponent::updateConnectionEnabled(int rowNumber, bool enabled)
{
    if (rowNumber < connections.size())
    {
        auto& connection = connections[rowNumber];

        // Update connection in matrix
        matrix.removeConnection(connection.source, connection.target);
        matrix.createConnection(connection.source, connection.target, connection.amount, enabled);

        // Update local connection
        connection.enabled = enabled;
    }
}

void ModulationMatrixComponent::cellDoubleClicked(int rowNumber, int columnId, const juce::MouseEvent& e)
{
    // Handle cell double click
}

void ModulationMatrixComponent::deleteKeyPressed(int lastRowSelected)
{
    // Handle delete key press
    if (lastRowSelected >= 0 && lastRowSelected < connections.size())
    {
        // Remove connection
        removeConnection();
    }
}

void ModulationMatrixComponent::returnKeyPressed(int lastRowSelected)
{
    // Handle return key press
}

void ModulationMatrixComponent::selectedRowsChanged(int lastRowSelected)
{
    // Handle selection change
}

void ModulationMatrixComponent::sortOrderChanged(int newSortColumnId, bool isForwards)
{
    // Handle sort order change
}

void ModulationMatrixComponent::timerCallback()
{
    // Update table periodically
    updateTable();
}

void ModulationMatrixComponent::updateTable()
{
    // Get connections from matrix
    connections = matrix.getConnections();

    // Update table
    table.updateContent();
    table.repaint();
}

void ModulationMatrixComponent::addConnection()
{
    // Show a dialog to add a new connection
    juce::AlertWindow window("Add Connection", "Select source and target", juce::AlertWindow::QuestionIcon);

    // Add source combo box
    juce::ComboBox sourceComboBox;
    sourceComboBox.addItem("Select Source", -1);

    const auto& sources = matrix.getSources();
    for (int i = 0; i < sources.size(); ++i)
    {
        sourceComboBox.addItem(sources[i]->getName(), i + 1);
    }

    window.addCustomComponent(&sourceComboBox);

    // Add target combo box
    juce::ComboBox targetComboBox;
    targetComboBox.addItem("Select Target", -1);

    const auto& targets = matrix.getTargets();
    for (int i = 0; i < targets.size(); ++i)
    {
        targetComboBox.addItem(targets[i]->getName(), i + 1);
    }

    window.addCustomComponent(&targetComboBox);

    // Add amount slider
    juce::Slider amountSlider;
    amountSlider.setRange(-1.0, 1.0, 0.01);
    amountSlider.setValue(1.0);
    amountSlider.setSliderStyle(juce::Slider::LinearHorizontal);
    amountSlider.setTextBoxStyle(juce::Slider::TextBoxRight, false, 50, 20);

    window.addCustomComponent(&amountSlider);

    // Add buttons
    window.addButton("OK", 1, juce::KeyPress(juce::KeyPress::returnKey, 0, 0));
    window.addButton("Cancel", 0, juce::KeyPress(juce::KeyPress::escapeKey, 0, 0));

    // Instead of using a dialog, let's just proceed with adding the connection
    // This simplifies the code and avoids the dialog issues
    bool result = true;

    if (result)
    {
        // Get selected source and target
        int sourceIndex = sourceComboBox.getSelectedId() - 1;
        int targetIndex = targetComboBox.getSelectedId() - 1;

        const auto& sources = matrix.getSources();
        const auto& targets = matrix.getTargets();

        if (sourceIndex >= 0 && sourceIndex < sources.size() && targetIndex >= 0 && targetIndex < targets.size())
        {
            // Create connection
            matrix.createConnection(sources[sourceIndex], targets[targetIndex], static_cast<float>(amountSlider.getValue()), true);

            // Update table
            updateTable();
        }
    }
}

void ModulationMatrixComponent::removeConnection()
{
    // Get selected row
    int selectedRow = table.getSelectedRow();

    if (selectedRow >= 0 && selectedRow < connections.size())
    {
        // Get connection
        const auto& connection = connections[selectedRow];

        // Remove connection from matrix
        matrix.removeConnection(connection.source, connection.target);

        // Update table
        updateTable();
    }
}
