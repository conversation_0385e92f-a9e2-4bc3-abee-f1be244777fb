/*
  ==============================================================================

    Emitter.cpp
    Created: 21 May 2023
    Author:  AuraBloom Team

    Implementation of the Emitter class.

  ==============================================================================
*/

#include "Emitter.h"

Emitter::Emitter()
{
}

Emitter::~Emitter()
{
}

void Emitter::initialize(ParticleSystem* particleSystem)
{
    this->particleSystem = particleSystem;
}

void Emitter::update(float deltaTime)
{
    if (particleSystem == nullptr)
        return;
    
    // Update time since last emission
    timeSinceLastEmission += deltaTime;
    
    // Check if it's time to emit a new particle
    float emissionInterval = getEmissionInterval();
    
    if (timeSinceLastEmission >= emissionInterval)
    {
        // Emit a particle
        emitParticle();
        
        // Reset timer
        timeSinceLastEmission = 0.0f;
    }
}

void Emitter::setPosition(float x, float y)
{
    this->x = x;
    this->y = y;
}

void Emitter::setShape(Shape shape)
{
    this->shape = shape;
}

void Emitter::setRate(float rate)
{
    this->rate = juce::jmax(0.1f, rate);
}

void Emitter::setBurstMode(bool burstMode)
{
    this->burstMode = burstMode;
}

void Emitter::setInitialVelocity(float velocity)
{
    this->initialVelocity = velocity;
}

void Emitter::setSpread(float spread)
{
    this->spread = spread;
}

void Emitter::setAudioInput(int inputIndex)
{
    this->audioInput = inputIndex;
}

void Emitter::setBufferPlaybackPosition(float position)
{
    this->bufferPlaybackPosition = juce::jlimit(0.0f, 100.0f, position);
}

void Emitter::setBufferScanSpeed(float speed)
{
    this->bufferScanSpeed = speed;
}

void Emitter::setBufferLoopMode(LoopMode mode)
{
    this->bufferLoopMode = mode;
}

void Emitter::emitParticle()
{
    if (particleSystem == nullptr)
        return;
    
    // Calculate emission position based on shape
    float emitX = x;
    float emitY = y;
    
    switch (shape)
    {
        case Point:
            // Use the emitter position directly
            break;
            
        case Line:
            // Emit along a horizontal line centered at the emitter position
            emitX = x + juce::Random::getSystemRandom().nextFloat() * 100.0f - 50.0f;
            break;
            
        case Circle:
            // Emit in a circle around the emitter position
            {
                float angle = juce::Random::getSystemRandom().nextFloat() * juce::MathConstants<float>::twoPi;
                float radius = juce::Random::getSystemRandom().nextFloat() * 50.0f;
                emitX = x + std::cos(angle) * radius;
                emitY = y + std::sin(angle) * radius;
            }
            break;
    }
    
    // Calculate initial velocity
    float angle = juce::Random::getSystemRandom().nextFloat() * juce::MathConstants<float>::twoPi;
    float spreadFactor = spread / 100.0f; // Convert 0-100 to 0-1
    
    // Add randomness to angle based on spread
    angle += (juce::Random::getSystemRandom().nextFloat() - 0.5f) * spreadFactor * juce::MathConstants<float>::pi;
    
    float velocityX = std::cos(angle) * initialVelocity;
    float velocityY = std::sin(angle) * initialVelocity;
    
    // Emit the particle
    particleSystem->emitParticle(emitX, emitY, velocityX, velocityY);
}

float Emitter::getEmissionInterval() const
{
    if (rate <= 0.0f)
        return std::numeric_limits<float>::max();
    
    return 1.0f / rate;
}
