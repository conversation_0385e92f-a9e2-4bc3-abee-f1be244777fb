/*
  ==============================================================================

    BloomChamberComponent.h
    Created: 21 May 2023
    Author:  AuraBloom Team

    Enhanced BloomChamberComponent - Interactive particle visualization with
    real-time rendering and user interaction capabilities.

  ==============================================================================
*/

#pragma once

#include <JuceHeader.h>
#include "ParticleSystem.h"
#include "GrainGenerator.h"

class BloomChamberComponent : public juce::Component,
                             private juce::Timer
{
public:
    BloomChamberComponent(ParticleSystem& particleSystem, GrainGenerator& grainGenerator);
    ~BloomChamberComponent() override;

    void paint(juce::Graphics& g) override;
    void resized() override;

    // Mouse interaction for particle manipulation and XY control
    void mouseDown(const juce::MouseEvent& e) override;
    void mouseDrag(const juce::MouseEvent& e) override;
    void mouseUp(const juce::MouseEvent& e) override;
    void mouseMove(const juce::MouseEvent& e) override;

    // Visual settings
    void setShowTrails(bool shouldShow) { showTrails = shouldShow; }
    void setParticleSize(float size) { particleSize = juce::jlimit(1.0f, 10.0f, size); }
    void setVisualizationMode(int mode) { visualizationMode = mode; }

    // XY Control settings
    void setXYControlEnabled(bool enabled) { xyControlEnabled = enabled; }
    void setShowXYGrid(bool show) { showXYGrid = show; }
    juce::Point<float> getXYPosition() const { return xyPosition; }

    // Parameter change callback for XY control
    std::function<void(const juce::String&, float)> onXYParameterChange;

private:
    // References to audio systems
    ParticleSystem& particleSystem;
    GrainGenerator& grainGenerator;

    // Timer callback for smooth animation
    void timerCallback() override;

    // Rendering methods
    void drawBackground(juce::Graphics& g);
    void drawParticles(juce::Graphics& g);
    void drawInteractionEffects(juce::Graphics& g);
    void drawInfoOverlay(juce::Graphics& g);

    // Interaction state
    bool isDragging = false;
    bool isAttractorMode = true; // true = attractor, false = repulsor
    juce::Point<float> mousePosition;
    juce::Point<float> dragStartPosition;
    float interactionRadius = 50.0f;
    float interactionStrength = 1.0f;

    // XY Control state
    bool xyControlEnabled = true;
    bool showXYGrid = true;
    bool isXYControlActive = false;
    juce::Point<float> xyPosition{0.5f, 0.5f}; // Normalized 0-1
    juce::Array<juce::Point<float>> xyTrail;
    static constexpr int maxXYTrailPoints = 30;

    // Visual settings
    bool showTrails = true;
    float particleSize = 3.0f;
    int visualizationMode = 0; // 0=dots, 1=lines, 2=glow

    // Animation state
    float animationTime = 0.0f;
    juce::Array<juce::Point<float>> particleTrails;

    // Performance optimization
    int frameSkipCounter = 0;
    static constexpr int maxFrameSkip = 2; // Update every 3rd frame for performance

    // Color mapping
    juce::Colour getParticleColor(float age, float velocity, float pitch) const;
    float getParticleAlpha(float age, float lifespan) const;

    // XY Control methods
    void updateXYParameters();
    void drawXYGrid(juce::Graphics& g);
    void drawXYControlPoint(juce::Graphics& g);
    void drawXYTrail(juce::Graphics& g);
    juce::Point<float> screenToNormalizedXY(juce::Point<float> screenPoint) const;
    juce::Point<float> normalizedToScreenXY(juce::Point<float> normalizedPoint) const;

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(BloomChamberComponent)
};
