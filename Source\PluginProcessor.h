/*
  ==============================================================================

    This file contains the basic framework code for a JUCE plugin processor.

  ==============================================================================
*/

#pragma once

#include <JuceHeader.h>
#include "PluginParameters.h"
#include "ParticleSystem.h"
#include "Emitter.h"
#include "AudioBufferManager.h"
#include "GrainGenerator.h"
#include "ModulationMatrix.h"
#include "LFO.h"
#include "Envelope.h"

// Debug logging macro
#define AURABLOOM_DEBUG 1
#if AURABLOOM_DEBUG
    #define DBG_LOG(x) juce::Logger::writeToLog("AuraBloom: " + juce::String(x))
#else
    #define DBG_LOG(x)
#endif

//==============================================================================
/**
*/
class AuraBloomAudioProcessor  : public juce::AudioProcessor
{
public:
    //==============================================================================
    AuraBloomAudioProcessor();
    ~AuraBloomAudioProcessor() override;

    //==============================================================================
    void prepareToPlay (double sampleRate, int samplesPerBlock) override;
    void releaseResources() override;

   #ifndef JucePlugin_PreferredChannelConfigurations
    bool isBusesLayoutSupported (const BusesLayout& layouts) const override;
   #endif

    void processBlock (juce::AudioBuffer<float>&, juce::MidiBuffer&) override;

    //==============================================================================
    juce::AudioProcessorEditor* createEditor() override;
    bool hasEditor() const override;

    //==============================================================================
    const juce::String getName() const override;

    bool acceptsMidi() const override;
    bool producesMidi() const override;
    bool isMidiEffect() const override;
    double getTailLengthSeconds() const override;

    //==============================================================================
    int getNumPrograms() override;
    int getCurrentProgram() override;
    void setCurrentProgram (int index) override;
    const juce::String getProgramName (int index) override;
    void changeProgramName (int index, const juce::String& newName) override;

    //==============================================================================
    void getStateInformation (juce::MemoryBlock& destData) override;
    void setStateInformation (const void* data, int sizeInBytes) override;

    //==============================================================================
    // Parameter access
    juce::AudioProcessorValueTreeState& getParameters() { return parameters; }

    // Get particle system for visualization
    ParticleSystem& getParticleSystem() { return particleSystem; }

    // Get audio buffer manager for visualization
    AudioBufferManager& getAudioBufferManager() { return audioBufferManager; }

    // Get grain generator
    GrainGenerator& getGrainGenerator() { return grainGenerator; }

    // Get modulation matrix
    ModulationMatrix& getModulationMatrix() { return modulationMatrix; }

private:
    //==============================================================================
    // Parameter management
    juce::AudioProcessorValueTreeState parameters;

    // Core components
    ParticleSystem particleSystem;
    Emitter emitter;
    AudioBufferManager audioBufferManager;
    GrainGenerator grainGenerator;
    ModulationMatrix modulationMatrix;

    // Audio effects
    juce::dsp::ProcessorDuplicator<juce::dsp::IIR::Filter<float>, juce::dsp::IIR::Coefficients<float>> lowPassFilter;
    juce::dsp::ProcessorDuplicator<juce::dsp::IIR::Filter<float>, juce::dsp::IIR::Coefficients<float>> highPassFilter;
    juce::dsp::ProcessorDuplicator<juce::dsp::IIR::Filter<float>, juce::dsp::IIR::Coefficients<float>> bandPassFilter;
    juce::dsp::ProcessorDuplicator<juce::dsp::IIR::Filter<float>, juce::dsp::IIR::Coefficients<float>> notchFilter;
    juce::dsp::DelayLine<float, juce::dsp::DelayLineInterpolationTypes::Linear> delayLine;
    juce::dsp::Reverb reverb;

    // DSP context
    juce::dsp::ProcessSpec spec;

    // Filter state
    int currentFilterType = 0;
    float currentCutoff = 1000.0f;
    float currentResonance = 1.0f;

    // Audio processing
    juce::AudioBuffer<float> inputBuffer;

    // Parameter listeners
    std::vector<std::unique_ptr<juce::AudioProcessorValueTreeState::Listener>> parameterListeners;

    // Update parameters from APVTS
    void updateParameters();

    // Map particle system to grain generator
    void mapParticleSystemToGrainGenerator();

    // Initialize modulation matrix
    void initializeModulationMatrix();

    // Audio effects methods
    void initializeAudioEffects();
    void updateAudioEffects();
    void processAudioEffects(juce::AudioBuffer<float>& buffer);

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR (AuraBloomAudioProcessor)
};
