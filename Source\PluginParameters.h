/*
  ==============================================================================

    PluginParameters.h
    Created: 21 May 2023
    Author:  AuraBloom Team

    This file contains parameter ID constants and parameter creation utilities.

  ==============================================================================
*/

#pragma once

#include <JuceHeader.h>

namespace AuraBloomParameters
{
    // Parameter IDs

    // Global Parameters
    inline const juce::String MIX_ID = "mix";
    inline const juce::String GAIN_ID = "gain";

    // Emitter Parameters
    inline const juce::String EMITTER_AUDIO_INPUT_ID = "emitterAudioInput";
    inline const juce::String EMITTER_SHAPE_ID = "emitterShape";
    inline const juce::String EMITTER_RATE_ID = "emitterRate";
    inline const juce::String EMITTER_BURST_MODE_ID = "emitterBurstMode";
    inline const juce::String EMITTER_INITIAL_VELOCITY_ID = "emitterInitialVelocity";
    inline const juce::String EMITTER_SPREAD_ID = "emitterSpread";

    // Buffer Control Parameters
    inline const juce::String BUFFER_PLAYBACK_POSITION_ID = "bufferPlaybackPosition";
    inline const juce::String BUFFER_SCAN_SPEED_ID = "bufferScanSpeed";
    inline const juce::String BUFFER_LOOP_MODE_ID = "bufferLoopMode";

    // Grain Engine Parameters
    inline const juce::String GRAIN_SIZE_ID = "grainSize";
    inline const juce::String GRAIN_ENVELOPE_ID = "grainEnvelope";
    inline const juce::String GRAIN_PITCH_SHIFT_ID = "grainPitchShift";
    inline const juce::String GRAIN_PAN_ID = "grainPan";
    inline const juce::String GRAIN_DENSITY_ID = "grainDensity";
    inline const juce::String GRAIN_PLAY_DIRECTION_ID = "grainPlayDirection";

    // Particle System Parameters
    inline const juce::String PARTICLE_LIFESPAN_ID = "particleLifespan";
    inline const juce::String PARTICLE_BEHAVIOR_MODE_ID = "particleBehaviorMode";
    inline const juce::String PARTICLE_BEHAVIOR_INTENSITY_ID = "particleBehaviorIntensity";
    inline const juce::String PARTICLE_INTERACTION_RADIUS_ID = "particleInteractionRadius";
    inline const juce::String PARTICLE_DAMPING_ID = "particleDamping";

    // Stage 5: Advanced Collision Parameters (NEW - SAFE TO ADD)
    inline const juce::String COLLISION_ADVANCED_ENABLED_ID = "collisionAdvancedEnabled";
    inline const juce::String COLLISION_ELASTICITY_ID = "collisionElasticity";
    inline const juce::String COLLISION_AUDIO_TRIGGERS_ID = "collisionAudioTriggers";

    // Macro Controls
    inline const juce::String MACRO_GENESIS_ID = "macroGenesis";
    inline const juce::String MACRO_SCATTER_ID = "macroScatter";
    inline const juce::String MACRO_FORM_ID = "macroForm";
    inline const juce::String MACRO_DRIFT_ID = "macroDrift";
    inline const juce::String MACRO_FLOW_ID = "macroFlow";
    inline const juce::String MACRO_EVOLVE_ID = "macroEvolve";
    inline const juce::String MACRO_AMBIENCE_ID = "macroAmbience";

    // Filter Parameters
    inline const juce::String FILTER_TYPE_ID = "filterType";
    inline const juce::String FILTER_CUTOFF_ID = "filterCutoff";
    inline const juce::String FILTER_RESONANCE_ID = "filterResonance";

    // Delay Parameters
    inline const juce::String DELAY_TIME_ID = "delayTime";
    inline const juce::String DELAY_FEEDBACK_ID = "delayFeedback";
    inline const juce::String DELAY_MIX_ID = "delayMix";

    // Reverb Parameters
    inline const juce::String REVERB_SIZE_ID = "reverbSize";
    inline const juce::String REVERB_DECAY_ID = "reverbDecay";

    // Parameter creation helpers
    inline juce::AudioProcessorValueTreeState::ParameterLayout createParameterLayout()
    {
        juce::AudioProcessorValueTreeState::ParameterLayout layout;

        // Global Parameters
        layout.add(std::make_unique<juce::AudioParameterFloat>(
            MIX_ID, "Mix", 0.0f, 100.0f, 100.0f));

        layout.add(std::make_unique<juce::AudioParameterFloat>(
            GAIN_ID, "Gain", -36.0f, 36.0f, 0.0f));

        // Emitter Parameters
        layout.add(std::make_unique<juce::AudioParameterChoice>(
            EMITTER_AUDIO_INPUT_ID, "Audio Input", juce::StringArray("Main Input"), 0));

        layout.add(std::make_unique<juce::AudioParameterChoice>(
            EMITTER_SHAPE_ID, "Emitter Shape", juce::StringArray("Point", "Line", "Circle"), 0));

        layout.add(std::make_unique<juce::AudioParameterFloat>(
            EMITTER_RATE_ID, "Rate", 0.1f, 100.0f, 10.0f));

        layout.add(std::make_unique<juce::AudioParameterBool>(
            EMITTER_BURST_MODE_ID, "Burst Mode", false));

        layout.add(std::make_unique<juce::AudioParameterFloat>(
            EMITTER_INITIAL_VELOCITY_ID, "Initial Velocity", 0.0f, 100.0f, 50.0f));

        layout.add(std::make_unique<juce::AudioParameterFloat>(
            EMITTER_SPREAD_ID, "Spread", 0.0f, 100.0f, 50.0f));

        // Buffer Control Parameters
        layout.add(std::make_unique<juce::AudioParameterFloat>(
            BUFFER_PLAYBACK_POSITION_ID, "Playback Position", 0.0f, 100.0f, 0.0f));

        layout.add(std::make_unique<juce::AudioParameterFloat>(
            BUFFER_SCAN_SPEED_ID, "Scan Speed", -200.0f, 200.0f, 0.0f));

        layout.add(std::make_unique<juce::AudioParameterChoice>(
            BUFFER_LOOP_MODE_ID, "Loop Mode", juce::StringArray("No Loop", "Forward", "Ping-Pong"), 0));

        // Grain Engine Parameters
        layout.add(std::make_unique<juce::AudioParameterFloat>(
            GRAIN_SIZE_ID, "Grain Size", 1.0f, 500.0f, 50.0f));

        layout.add(std::make_unique<juce::AudioParameterChoice>(
            GRAIN_ENVELOPE_ID, "Grain Envelope", juce::StringArray("Gaussian", "Triangle", "Rectangle", "Cosine"), 0));

        layout.add(std::make_unique<juce::AudioParameterFloat>(
            GRAIN_PITCH_SHIFT_ID, "Pitch Shift", -24.0f, 24.0f, 0.0f));

        layout.add(std::make_unique<juce::AudioParameterFloat>(
            GRAIN_PAN_ID, "Pan", -100.0f, 100.0f, 0.0f));

        layout.add(std::make_unique<juce::AudioParameterFloat>(
            GRAIN_DENSITY_ID, "Density", 1.0f, 100.0f, 50.0f));

        layout.add(std::make_unique<juce::AudioParameterChoice>(
            GRAIN_PLAY_DIRECTION_ID, "Play Direction", juce::StringArray("Forward", "Backward", "Random"), 0));

        // Particle System Parameters
        layout.add(std::make_unique<juce::AudioParameterFloat>(
            PARTICLE_LIFESPAN_ID, "Lifespan", 0.1f, 10.0f, 2.0f));

        layout.add(std::make_unique<juce::AudioParameterChoice>(
            PARTICLE_BEHAVIOR_MODE_ID, "Behavior Mode", juce::StringArray("Swarm", "Repel", "Attract", "Orbit"), 0));

        layout.add(std::make_unique<juce::AudioParameterFloat>(
            PARTICLE_BEHAVIOR_INTENSITY_ID, "Behavior Intensity", 0.0f, 100.0f, 50.0f));

        layout.add(std::make_unique<juce::AudioParameterFloat>(
            PARTICLE_INTERACTION_RADIUS_ID, "Interaction Radius", 1.0f, 100.0f, 20.0f));

        layout.add(std::make_unique<juce::AudioParameterFloat>(
            PARTICLE_DAMPING_ID, "Damping", 0.0f, 100.0f, 10.0f));

        // Macro Controls
        layout.add(std::make_unique<juce::AudioParameterFloat>(
            MACRO_GENESIS_ID, "Genesis", 0.0f, 100.0f, 50.0f));

        layout.add(std::make_unique<juce::AudioParameterFloat>(
            MACRO_SCATTER_ID, "Scatter", 0.0f, 100.0f, 50.0f));

        layout.add(std::make_unique<juce::AudioParameterFloat>(
            MACRO_FORM_ID, "Form", 0.0f, 100.0f, 50.0f));

        layout.add(std::make_unique<juce::AudioParameterFloat>(
            MACRO_DRIFT_ID, "Drift", 0.0f, 100.0f, 50.0f));

        layout.add(std::make_unique<juce::AudioParameterFloat>(
            MACRO_FLOW_ID, "Flow", 0.0f, 100.0f, 50.0f));

        layout.add(std::make_unique<juce::AudioParameterFloat>(
            MACRO_EVOLVE_ID, "Evolve", 0.0f, 100.0f, 50.0f));

        layout.add(std::make_unique<juce::AudioParameterFloat>(
            MACRO_AMBIENCE_ID, "Ambience", 0.0f, 100.0f, 50.0f));

        // Filter Parameters
        layout.add(std::make_unique<juce::AudioParameterChoice>(
            FILTER_TYPE_ID, "Filter Type", juce::StringArray("Low Pass", "High Pass", "Band Pass", "Notch"), 0));

        layout.add(std::make_unique<juce::AudioParameterFloat>(
            FILTER_CUTOFF_ID, "Cutoff", 20.0f, 20000.0f, 1000.0f));

        layout.add(std::make_unique<juce::AudioParameterFloat>(
            FILTER_RESONANCE_ID, "Resonance", 0.1f, 10.0f, 1.0f));

        // Delay Parameters
        layout.add(std::make_unique<juce::AudioParameterFloat>(
            DELAY_TIME_ID, "Delay Time", 0.01f, 2.0f, 0.5f));

        layout.add(std::make_unique<juce::AudioParameterFloat>(
            DELAY_FEEDBACK_ID, "Feedback", 0.0f, 100.0f, 50.0f));

        layout.add(std::make_unique<juce::AudioParameterFloat>(
            DELAY_MIX_ID, "Delay Mix", 0.0f, 100.0f, 50.0f));

        // Reverb Parameters
        layout.add(std::make_unique<juce::AudioParameterFloat>(
            REVERB_SIZE_ID, "Reverb Size", 0.0f, 100.0f, 50.0f));

        layout.add(std::make_unique<juce::AudioParameterFloat>(
            REVERB_DECAY_ID, "Decay", 0.1f, 10.0f, 2.0f));

        // Stage 5: Advanced Collision Parameters (SAFE - ADDED AT END)
        layout.add(std::make_unique<juce::AudioParameterBool>(
            COLLISION_ADVANCED_ENABLED_ID, "Advanced Collisions", false));

        layout.add(std::make_unique<juce::AudioParameterFloat>(
            COLLISION_ELASTICITY_ID, "Collision Elasticity", 0.0f, 100.0f, 80.0f));

        layout.add(std::make_unique<juce::AudioParameterBool>(
            COLLISION_AUDIO_TRIGGERS_ID, "Collision Audio", true));

        return layout;
    }
}
