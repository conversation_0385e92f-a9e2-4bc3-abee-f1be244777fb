# AuraBloom Windows构建包

## 快速开始

1. **安装Visual Studio 2022** (Community版免费)
   - 确保安装"使用C++的桌面开发"工作负载

2. **安装JUCE Framework**
   - 下载并安装到 `C:\JUCE\`

3. **构建插件**
   ```cmd
   build_windows.bat Release x64
   ```

4. **安装插件**
   - 将生成的 `.vst3` 文件夹复制到:
   - `C:\Program Files\Common Files\VST3\`

## 文件说明

- `Source/` - 插件源代码
- `Builds/VisualStudio2022/` - Visual Studio项目文件
- `JuceLibraryCode/` - JUCE库代码
- `build_windows.bat` - 自动构建脚本
- `Windows_Build_Instructions.md` - 详细构建说明

## 技术支持

如遇问题，请参考 `Windows_Build_Instructions.md` 中的故障排除部分。

## 版本信息

- 版本: 1.0.0
- 构建日期: $(date)
- 支持平台: Windows 10/11 (64位)
- 插件格式: VST3
