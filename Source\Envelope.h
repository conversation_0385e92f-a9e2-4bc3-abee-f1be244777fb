/*
  ==============================================================================

    Envelope.h
    Created: 21 May 2023
    Author:  AuraBloom Team

    This file defines the Envelope class, which represents an ADSR envelope
    modulation source.

  ==============================================================================
*/

#pragma once

#include <JuceHeader.h>
#include "ModulationSource.h"

/**
 * ADSR envelope modulation source.
 * 
 * An envelope generates a time-varying signal based on attack, decay,
 * sustain, and release parameters.
 */
class Envelope : public ModulationSource
{
public:
    /**
     * Envelope stages.
     */
    enum Stage
    {
        Idle,       // Envelope is idle (not triggered)
        Attack,     // Attack stage
        Decay,      // Decay stage
        Sustain,    // Sustain stage
        Release     // Release stage
    };
    
    /**
     * Trigger modes for the envelope.
     */
    enum TriggerMode
    {
        Gate,       // Envelope follows gate (note on/off)
        Trigger,    // Envelope is triggered and completes its cycle
        Loop,       // Envelope loops continuously
        OneShot     // Envelope plays once when triggered
    };
    
    /**
     * Curve types for envelope segments.
     */
    enum CurveType
    {
        Linear,     // Linear curve
        Exponential,// Exponential curve
        Logarithmic,// Logarithmic curve
        SCurve      // S-curve (sigmoid)
    };
    
    /**
     * Constructor.
     * 
     * @param name The name of the envelope
     */
    Envelope(const juce::String& name = "Envelope");
    
    /**
     * Destructor.
     */
    ~Envelope() override;
    
    /**
     * Reset the envelope.
     */
    void reset() override;
    
    /**
     * Update the envelope.
     * 
     * @param deltaTime Time elapsed since last update in seconds
     */
    void update(float deltaTime) override;
    
    /**
     * Get the envelope value at a specific time offset.
     * 
     * @param timeOffset Time offset in seconds
     * @return The envelope value at the specified time offset
     */
    float getValueAt(float timeOffset) const override;
    
    /**
     * Set the attack time in seconds.
     * 
     * @param timeSeconds The attack time in seconds
     */
    void setAttackTime(float timeSeconds);
    
    /**
     * Get the attack time in seconds.
     * 
     * @return The attack time in seconds
     */
    float getAttackTime() const;
    
    /**
     * Set the decay time in seconds.
     * 
     * @param timeSeconds The decay time in seconds
     */
    void setDecayTime(float timeSeconds);
    
    /**
     * Get the decay time in seconds.
     * 
     * @return The decay time in seconds
     */
    float getDecayTime() const;
    
    /**
     * Set the sustain level (0.0 to 1.0).
     * 
     * @param level The sustain level
     */
    void setSustainLevel(float level);
    
    /**
     * Get the sustain level.
     * 
     * @return The sustain level
     */
    float getSustainLevel() const;
    
    /**
     * Set the release time in seconds.
     * 
     * @param timeSeconds The release time in seconds
     */
    void setReleaseTime(float timeSeconds);
    
    /**
     * Get the release time in seconds.
     * 
     * @return The release time in seconds
     */
    float getReleaseTime() const;
    
    /**
     * Set the trigger mode.
     * 
     * @param mode The trigger mode
     */
    void setTriggerMode(TriggerMode mode);
    
    /**
     * Get the trigger mode.
     * 
     * @return The trigger mode
     */
    TriggerMode getTriggerMode() const;
    
    /**
     * Set the curve type for a specific stage.
     * 
     * @param stage The envelope stage
     * @param type The curve type
     */
    void setCurveType(Stage stage, CurveType type);
    
    /**
     * Get the curve type for a specific stage.
     * 
     * @param stage The envelope stage
     * @return The curve type
     */
    CurveType getCurveType(Stage stage) const;
    
    /**
     * Trigger the envelope (note on).
     */
    void noteOn();
    
    /**
     * Release the envelope (note off).
     */
    void noteOff();
    
    /**
     * Get the current envelope stage.
     * 
     * @return The current envelope stage
     */
    Stage getCurrentStage() const;
    
    /**
     * Get whether the envelope is active (not in Idle stage).
     * 
     * @return True if the envelope is active
     */
    bool isEnvelopeActive() const;
    
private:
    Stage currentStage = Idle;
    TriggerMode triggerMode = Gate;
    
    float attackTime = 0.01f;  // seconds
    float decayTime = 0.1f;    // seconds
    float sustainLevel = 0.7f;  // 0.0 to 1.0
    float releaseTime = 0.5f;  // seconds
    
    float stageProgress = 0.0f; // 0.0 to 1.0
    float envelopeValue = 0.0f; // Current envelope value
    float releaseStartValue = 0.0f; // Value at the start of release stage
    
    CurveType attackCurve = Exponential;
    CurveType decayCurve = Exponential;
    CurveType releaseCurve = Exponential;
    
    /**
     * Calculate the envelope value for a specific stage and progress.
     * 
     * @param stage The envelope stage
     * @param progress The progress through the stage (0.0 to 1.0)
     * @return The envelope value
     */
    float calculateStageValue(Stage stage, float progress) const;
    
    /**
     * Apply a curve to a linear progress value.
     * 
     * @param progress The linear progress (0.0 to 1.0)
     * @param curveType The curve type to apply
     * @return The curved progress value
     */
    float applyCurve(float progress, CurveType curveType) const;
    
    /**
     * Advance to the next stage.
     */
    void advanceToNextStage();
    
    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(Envelope)
};
