/*
  ==============================================================================

    ModulationMatrix.h
    Created: 21 May 2023
    Author:  AuraBloom Team

    This file defines the ModulationMatrix class, which manages connections
    between modulation sources and targets.

  ==============================================================================
*/

#pragma once

#include <JuceHeader.h>
#include "ModulationSource.h"
#include "ModulationTarget.h"
#include "ParameterModulationTarget.h"

/**
 * A connection between a modulation source and target.
 */
struct ModulationConnection
{
    ModulationSource* source;
    ModulationTarget* target;
    float amount;
    bool enabled;
    
    ModulationConnection(ModulationSource* source, ModulationTarget* target, float amount = 1.0f, bool enabled = true)
        : source(source), target(target), amount(amount), enabled(enabled)
    {
    }
};

/**
 * Manages connections between modulation sources and targets.
 */
class ModulationMatrix
{
public:
    /**
     * Constructor.
     */
    ModulationMatrix();
    
    /**
     * Destructor.
     */
    ~ModulationMatrix();
    
    /**
     * Add a modulation source.
     * 
     * @param source The modulation source to add
     * @param takeOwnership Whether to take ownership of the source
     * @return True if the source was added successfully
     */
    bool addSource(ModulationSource* source, bool takeOwnership = true);
    
    /**
     * Remove a modulation source.
     * 
     * @param source The modulation source to remove
     * @return True if the source was removed successfully
     */
    bool removeSource(ModulationSource* source);
    
    /**
     * Get a modulation source by name.
     * 
     * @param name The name of the modulation source
     * @return The modulation source, or nullptr if not found
     */
    ModulationSource* getSource(const juce::String& name) const;
    
    /**
     * Get all modulation sources.
     * 
     * @return The modulation sources
     */
    const std::vector<ModulationSource*>& getSources() const;
    
    /**
     * Add a modulation target.
     * 
     * @param target The modulation target to add
     * @param takeOwnership Whether to take ownership of the target
     * @return True if the target was added successfully
     */
    bool addTarget(ModulationTarget* target, bool takeOwnership = true);
    
    /**
     * Remove a modulation target.
     * 
     * @param target The modulation target to remove
     * @return True if the target was removed successfully
     */
    bool removeTarget(ModulationTarget* target);
    
    /**
     * Get a modulation target by name.
     * 
     * @param name The name of the modulation target
     * @return The modulation target, or nullptr if not found
     */
    ModulationTarget* getTarget(const juce::String& name) const;
    
    /**
     * Get a parameter modulation target by parameter ID.
     * 
     * @param parameterID The parameter ID
     * @return The parameter modulation target, or nullptr if not found
     */
    ParameterModulationTarget* getParameterTarget(const juce::String& parameterID) const;
    
    /**
     * Get all modulation targets.
     * 
     * @return The modulation targets
     */
    const std::vector<ModulationTarget*>& getTargets() const;
    
    /**
     * Create a connection between a source and target.
     * 
     * @param source The modulation source
     * @param target The modulation target
     * @param amount The modulation amount
     * @param enabled Whether the connection is enabled
     * @return True if the connection was created successfully
     */
    bool createConnection(ModulationSource* source, ModulationTarget* target, float amount = 1.0f, bool enabled = true);
    
    /**
     * Create a connection between a source and target by name.
     * 
     * @param sourceName The name of the modulation source
     * @param targetName The name of the modulation target
     * @param amount The modulation amount
     * @param enabled Whether the connection is enabled
     * @return True if the connection was created successfully
     */
    bool createConnection(const juce::String& sourceName, const juce::String& targetName, float amount = 1.0f, bool enabled = true);
    
    /**
     * Remove a connection between a source and target.
     * 
     * @param source The modulation source
     * @param target The modulation target
     * @return True if the connection was removed successfully
     */
    bool removeConnection(ModulationSource* source, ModulationTarget* target);
    
    /**
     * Remove a connection between a source and target by name.
     * 
     * @param sourceName The name of the modulation source
     * @param targetName The name of the modulation target
     * @return True if the connection was removed successfully
     */
    bool removeConnection(const juce::String& sourceName, const juce::String& targetName);
    
    /**
     * Get all connections.
     * 
     * @return The connections
     */
    const std::vector<ModulationConnection>& getConnections() const;
    
    /**
     * Get connections for a specific source.
     * 
     * @param source The modulation source
     * @return The connections for the source
     */
    std::vector<ModulationConnection> getConnectionsForSource(ModulationSource* source) const;
    
    /**
     * Get connections for a specific target.
     * 
     * @param target The modulation target
     * @return The connections for the target
     */
    std::vector<ModulationConnection> getConnectionsForTarget(ModulationTarget* target) const;
    
    /**
     * Update all modulation sources.
     * 
     * @param deltaTime Time elapsed since last update in seconds
     */
    void updateSources(float deltaTime);
    
    /**
     * Apply modulation to all targets.
     */
    void applyModulation();
    
    /**
     * Reset all modulation sources and targets.
     */
    void reset();
    
    /**
     * Create parameter modulation targets from an AudioProcessorValueTreeState.
     * 
     * @param apvts The AudioProcessorValueTreeState
     */
    void createParameterTargets(juce::AudioProcessorValueTreeState& apvts);
    
private:
    std::vector<ModulationSource*> sources;
    std::vector<ModulationTarget*> targets;
    std::vector<ModulationConnection> connections;
    
    std::vector<std::unique_ptr<ModulationSource>> ownedSources;
    std::vector<std::unique_ptr<ModulationTarget>> ownedTargets;
    
    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(ModulationMatrix)
};
