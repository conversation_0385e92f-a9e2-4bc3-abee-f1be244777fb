/*
  ==============================================================================

    ParameterModulationTarget.cpp
    Created: 21 May 2023
    Author:  AuraBloom Team

    Implementation of the ParameterModulationTarget class.

  ==============================================================================
*/

#include "ParameterModulationTarget.h"

ParameterModulationTarget::ParameterModulationTarget(juce::RangedAudioParameter* parameter, const juce::String& parameterID)
    : ModulationTarget(parameter->getName(100)), parameter(parameter), parameterID(parameterID)
{
    // Set min and max values from parameter
    minValue = 0.0f;
    maxValue = 1.0f;
    
    // Set bipolar flag based on parameter range
    auto range = parameter->getNormalisableRange();
    bipolar = range.start < 0.0f && range.end > 0.0f;
    
    // Initialize base value from parameter
    updateBaseValueFromParameter();
}

ParameterModulationTarget::~ParameterModulationTarget()
{
}

void ParameterModulationTarget::applyValue()
{
    if (parameter != nullptr)
    {
        // Convert from actual value to normalized value (0.0 to 1.0)
        auto range = parameter->getNormalisableRange();
        float normalizedValue = range.convertTo0to1(currentValue);
        
        // Set the parameter value
        parameter->setValueNotifyingHost(normalizedValue);
    }
}

const juce::String& ParameterModulationTarget::getParameterID() const
{
    return parameterID;
}

juce::RangedAudioParameter* ParameterModulationTarget::getParameter() const
{
    return parameter;
}

void ParameterModulationTarget::updateBaseValueFromParameter()
{
    if (parameter != nullptr)
    {
        // Get the normalized value (0.0 to 1.0)
        float normalizedValue = parameter->getValue();
        
        // Convert to actual value
        auto range = parameter->getNormalisableRange();
        baseValue = range.convertFrom0to1(normalizedValue);
        
        // Update the current value
        update();
    }
}
