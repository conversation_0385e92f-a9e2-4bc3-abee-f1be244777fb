/*
  ==============================================================================

    ForceField.h
    Created: 21 May 2023
    Author:  AuraBloom Team

    This file defines the ForceField class hierarchy, which represents different
    types of force fields that can affect particles.

  ==============================================================================
*/

#pragma once

#include <JuceHeader.h>
#include "Particle.h"

/**
 * Abstract base class for all force fields
 */
class ForceField
{
public:
    ForceField() {}
    virtual ~ForceField() {}
    
    /**
     * Apply the force field to a particle
     * 
     * @param particle The particle to apply the force to
     * @param deltaTime Time elapsed since last update
     */
    virtual void applyToParticle(Particle& particle, float deltaTime) = 0;
    
    /**
     * Set the strength of the force field
     * 
     * @param strength The strength value
     */
    void setStrength(float strength) { this->strength = strength; }
    
    /**
     * Get the strength of the force field
     * 
     * @return The strength value
     */
    float getStrength() const { return strength; }
    
    /**
     * Set whether the force field is active
     * 
     * @param active Whether the force field is active
     */
    void setActive(bool active) { this->active = active; }
    
    /**
     * Check if the force field is active
     * 
     * @return Whether the force field is active
     */
    bool isActive() const { return active; }
    
protected:
    float strength = 1.0f;
    bool active = true;
};

/**
 * Point force field that attracts or repels particles from a point
 */
class PointForceField : public ForceField
{
public:
    PointForceField(float x, float y, float strength, bool attracting = true)
        : position(x, y), attracting(attracting)
    {
        this->strength = strength;
    }
    
    void applyToParticle(Particle& particle, float deltaTime) override
    {
        if (!active)
            return;
        
        // Calculate direction vector
        float dx = position.x - particle.x;
        float dy = position.y - particle.y;
        float distSq = dx * dx + dy * dy;
        
        // Avoid division by zero and very small values
        if (distSq < 0.0001f)
            return;
        
        float dist = std::sqrt(distSq);
        
        // Calculate force magnitude (inverse square law)
        float forceMag = strength / distSq;
        
        // Invert force if repelling
        if (!attracting)
            forceMag = -forceMag;
        
        // Apply force
        float fx = dx / dist * forceMag;
        float fy = dy / dist * forceMag;
        
        particle.applyForceWithMass(fx, fy);
    }
    
    void setPosition(float x, float y)
    {
        position.x = x;
        position.y = y;
    }
    
    void setAttracting(bool attracting)
    {
        this->attracting = attracting;
    }
    
    juce::Point<float> getPosition() const
    {
        return position;
    }
    
    bool isAttracting() const
    {
        return attracting;
    }
    
private:
    juce::Point<float> position;
    bool attracting;
};

/**
 * Directional force field that applies a constant force in a direction
 */
class DirectionalForceField : public ForceField
{
public:
    DirectionalForceField(float directionX, float directionY, float strength)
        : direction(directionX, directionY)
    {
        this->strength = strength;
        // Normalize direction
        float length = std::sqrt(direction.x * direction.x + direction.y * direction.y);
        if (length > 0.0001f)
        {
            direction.x /= length;
            direction.y /= length;
        }
    }
    
    void applyToParticle(Particle& particle, float deltaTime) override
    {
        if (!active)
            return;
        
        // Apply directional force
        float fx = direction.x * strength;
        float fy = direction.y * strength;
        
        particle.applyForceWithMass(fx, fy);
    }
    
    void setDirection(float directionX, float directionY)
    {
        direction.x = directionX;
        direction.y = directionY;
        
        // Normalize direction
        float length = std::sqrt(direction.x * direction.x + direction.y * direction.y);
        if (length > 0.0001f)
        {
            direction.x /= length;
            direction.y /= length;
        }
    }
    
    juce::Point<float> getDirection() const
    {
        return direction;
    }
    
private:
    juce::Point<float> direction;
};

/**
 * Vortex force field that creates a swirling effect
 */
class VortexForceField : public ForceField
{
public:
    VortexForceField(float x, float y, float strength, float radius = 50.0f)
        : center(x, y), radius(radius)
    {
        this->strength = strength;
    }
    
    void applyToParticle(Particle& particle, float deltaTime) override
    {
        if (!active)
            return;
        
        // Calculate direction vector
        float dx = particle.x - center.x;
        float dy = particle.y - center.y;
        float distSq = dx * dx + dy * dy;
        
        // Check if particle is within radius
        if (distSq > radius * radius)
            return;
        
        float dist = std::sqrt(distSq);
        
        // Avoid division by zero
        if (dist < 0.0001f)
            return;
        
        // Calculate perpendicular force for vortex effect
        float forceMag = strength * (1.0f - dist / radius);
        float fx = -dy / dist * forceMag;
        float fy = dx / dist * forceMag;
        
        particle.applyForceWithMass(fx, fy);
    }
    
    void setCenter(float x, float y)
    {
        center.x = x;
        center.y = y;
    }
    
    void setRadius(float radius)
    {
        this->radius = radius;
    }
    
    juce::Point<float> getCenter() const
    {
        return center;
    }
    
    float getRadius() const
    {
        return radius;
    }
    
private:
    juce::Point<float> center;
    float radius;
};

/**
 * Noise force field that applies random forces based on Perlin noise
 */
class NoiseForceField : public ForceField
{
public:
    NoiseForceField(float strength, float scale = 0.01f, float timeScale = 0.1f)
        : scale(scale), timeScale(timeScale), time(0.0f)
    {
        this->strength = strength;
    }
    
    void applyToParticle(Particle& particle, float deltaTime) override
    {
        if (!active)
            return;
        
        // Calculate noise value based on position and time
        float noiseX = juce::Random::getSystemRandom().nextFloat() * 2.0f - 1.0f;
        float noiseY = juce::Random::getSystemRandom().nextFloat() * 2.0f - 1.0f;
        
        // Apply noise force
        float fx = noiseX * strength;
        float fy = noiseY * strength;
        
        particle.applyForceWithMass(fx, fy);
    }
    
    void update(float deltaTime)
    {
        time += deltaTime * timeScale;
    }
    
    void setScale(float scale)
    {
        this->scale = scale;
    }
    
    void setTimeScale(float timeScale)
    {
        this->timeScale = timeScale;
    }
    
    float getScale() const
    {
        return scale;
    }
    
    float getTimeScale() const
    {
        return timeScale;
    }
    
private:
    float scale;
    float timeScale;
    float time;
};
