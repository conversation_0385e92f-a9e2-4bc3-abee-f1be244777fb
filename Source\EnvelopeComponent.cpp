/*
  ==============================================================================

    EnvelopeComponent.cpp
    Created: 21 May 2023
    Author:  AuraBloom Team

    Implementation of the EnvelopeComponent class.

  ==============================================================================
*/

#include "EnvelopeComponent.h"

EnvelopeComponent::EnvelopeComponent(Envelope* envelope)
    : ModulationSourceComponent(envelope)
{
    // Set up attack slider
    setupSlider(attackSlider, attackLabel, "Attack");
    attackSlider.setRange(0.001, 10.0, 0.001);
    attackSlider.setValue(getEnvelope()->getAttackTime(), juce::dontSendNotification);
    attackSlider.onValueChange = [this] { attackChanged(); };

    // Set up decay slider
    setupSlider(decaySlider, decayLabel, "Decay");
    decaySlider.setRange(0.001, 10.0, 0.001);
    decaySlider.setValue(getEnvelope()->getDecayTime(), juce::dontSendNotification);
    decaySlider.onValueChange = [this] { decayChanged(); };

    // Set up sustain slider
    setupSlider(sustainSlider, sustainLabel, "Sustain");
    sustainSlider.setRange(0.0, 1.0, 0.01);
    sustainSlider.setValue(getEnvelope()->getSustainLevel(), juce::dontSendNotification);
    sustainSlider.onValueChange = [this] { sustainChanged(); };

    // Set up release slider
    setupSlider(releaseSlider, releaseLabel, "Release");
    releaseSlider.setRange(0.001, 10.0, 0.001);
    releaseSlider.setValue(getEnvelope()->getReleaseTime(), juce::dontSendNotification);
    releaseSlider.onValueChange = [this] { releaseChanged(); };

    // Set up trigger mode combo box
    setupComboBox(triggerModeComboBox, triggerModeLabel, "Trigger Mode");
    triggerModeComboBox.addItem("Gate", Envelope::Gate + 1);
    triggerModeComboBox.addItem("Trigger", Envelope::Trigger + 1);
    triggerModeComboBox.addItem("Loop", Envelope::Loop + 1);
    triggerModeComboBox.addItem("One Shot", Envelope::OneShot + 1);
    triggerModeComboBox.setSelectedId(getEnvelope()->getTriggerMode() + 1);
    triggerModeComboBox.onChange = [this] { triggerModeChanged(); };

    // Set up trigger button
    setupButton(triggerButton, "Trigger");
    triggerButton.onClick = [this] { triggerClicked(); };

    // Set up release button
    setupButton(releaseButton, "Release");
    releaseButton.onClick = [this] { releaseClicked(); };
}

EnvelopeComponent::~EnvelopeComponent()
{
}

void EnvelopeComponent::resized()
{
    auto bounds = getLocalBounds().reduced(4);

    // Header area
    bounds.removeFromTop(30);

    // Waveform display area
    bounds.removeFromTop(bounds.getHeight() / 3);

    // Controls area
    auto controlsArea = bounds;

    // First row: Attack and Decay
    auto row1 = controlsArea.removeFromTop(50);
    auto attackArea = row1.removeFromLeft(row1.getWidth() / 2);
    auto decayArea = row1;

    attackLabel.setBounds(attackArea.removeFromTop(20));
    attackSlider.setBounds(attackArea);

    decayLabel.setBounds(decayArea.removeFromTop(20));
    decaySlider.setBounds(decayArea);

    // Second row: Sustain and Release
    auto row2 = controlsArea.removeFromTop(50);
    auto sustainArea = row2.removeFromLeft(row2.getWidth() / 2);
    auto releaseArea = row2;

    sustainLabel.setBounds(sustainArea.removeFromTop(20));
    sustainSlider.setBounds(sustainArea);

    releaseLabel.setBounds(releaseArea.removeFromTop(20));
    releaseSlider.setBounds(releaseArea);

    // Third row: Trigger Mode and Buttons
    auto row3 = controlsArea.removeFromTop(50);
    auto triggerModeArea = row3.removeFromLeft(row3.getWidth() / 2);
    auto buttonsArea = row3;

    triggerModeLabel.setBounds(triggerModeArea.removeFromTop(20));
    triggerModeComboBox.setBounds(triggerModeArea);

    auto triggerButtonArea = buttonsArea.removeFromLeft(buttonsArea.getWidth() / 2);
    auto releaseButtonArea = buttonsArea;

    triggerButton.setBounds(triggerButtonArea.reduced(5));
    releaseButton.setBounds(releaseButtonArea.reduced(5));
}

void EnvelopeComponent::drawWaveform(juce::Graphics& g, juce::Rectangle<int> bounds)
{
    if (getEnvelope() == nullptr)
        return;

    // Set up colors
    g.setColour(juce::Colour(30, 30, 60));
    g.fillRoundedRectangle(bounds.toFloat(), 4.0f);

    // Draw envelope shape
    g.setColour(active ? juce::Colour(100, 100, 255) : juce::Colour(80, 80, 120));

    auto envelope = getEnvelope();
    float attackTime = envelope->getAttackTime();
    float decayTime = envelope->getDecayTime();
    float sustainLevel = envelope->getSustainLevel();
    float releaseTime = envelope->getReleaseTime();

    // Calculate total time and scale factors
    float totalTime = attackTime + decayTime + 0.5f + releaseTime; // 0.5s for sustain display
    float timeScale = bounds.getWidth() / totalTime;
    float heightScale = bounds.getHeight() * 0.8f;
    float baseY = bounds.getY() + bounds.getHeight() * 0.9f;

    // Create path for envelope shape
    juce::Path envelopePath;
    envelopePath.startNewSubPath(bounds.getX(), baseY);

    // Attack
    float attackEndX = bounds.getX() + attackTime * timeScale;
    float attackEndY = baseY - heightScale;
    envelopePath.lineTo(attackEndX, attackEndY);

    // Decay
    float decayEndX = attackEndX + decayTime * timeScale;
    float decayEndY = baseY - sustainLevel * heightScale;
    envelopePath.lineTo(decayEndX, decayEndY);

    // Sustain
    float sustainEndX = decayEndX + 0.5f * timeScale;
    envelopePath.lineTo(sustainEndX, decayEndY);

    // Release
    float releaseEndX = sustainEndX + releaseTime * timeScale;
    envelopePath.lineTo(releaseEndX, baseY);

    // Draw the path
    g.strokePath(envelopePath, juce::PathStrokeType(2.0f));

    // Draw stage labels
    g.setColour(juce::Colours::white);
    g.setFont(juce::FontOptions (12.0f));

    float attackMidX = bounds.getX() + attackTime * timeScale / 2;
    g.drawText("A", attackMidX - 5, baseY - heightScale / 2, 10, 20, juce::Justification::centred, true);

    float decayMidX = attackEndX + decayTime * timeScale / 2;
    g.drawText("D", decayMidX - 5, baseY - (1.0f + sustainLevel) * heightScale / 2, 10, 20, juce::Justification::centred, true);

    float sustainMidX = decayEndX + 0.25f * timeScale;
    g.drawText("S", sustainMidX - 5, baseY - sustainLevel * heightScale / 2, 10, 20, juce::Justification::centred, true);

    float releaseMidX = sustainEndX + releaseTime * timeScale / 2;
    g.drawText("R", releaseMidX - 5, baseY - sustainLevel * heightScale / 4, 10, 20, juce::Justification::centred, true);
}

void EnvelopeComponent::drawControls(juce::Graphics& g, juce::Rectangle<int> bounds)
{
    // Controls are drawn by the JUCE components
}

Envelope* EnvelopeComponent::getEnvelope() const
{
    return static_cast<Envelope*>(source);
}

void EnvelopeComponent::setupSlider(juce::Slider& slider, juce::Label& label, const juce::String& labelText)
{
    // Set up label
    label.setText(labelText, juce::dontSendNotification);
    label.setJustificationType(juce::Justification::centred);
    label.setColour(juce::Label::textColourId, juce::Colours::white);
    addAndMakeVisible(label);

    // Set up slider
    slider.setSliderStyle(juce::Slider::LinearHorizontal);
    slider.setTextBoxStyle(juce::Slider::TextBoxRight, false, 60, 20);
    slider.setColour(juce::Slider::trackColourId, juce::Colour(60, 60, 100));
    slider.setColour(juce::Slider::thumbColourId, juce::Colour(100, 100, 255));
    slider.setColour(juce::Slider::textBoxTextColourId, juce::Colours::white);
    slider.setColour(juce::Slider::textBoxOutlineColourId, juce::Colours::transparentBlack);
    addAndMakeVisible(slider);
}

void EnvelopeComponent::setupComboBox(juce::ComboBox& comboBox, juce::Label& label, const juce::String& labelText)
{
    // Set up label
    label.setText(labelText, juce::dontSendNotification);
    label.setJustificationType(juce::Justification::centred);
    label.setColour(juce::Label::textColourId, juce::Colours::white);
    addAndMakeVisible(label);

    // Set up combo box
    comboBox.setColour(juce::ComboBox::backgroundColourId, juce::Colour(60, 60, 100));
    comboBox.setColour(juce::ComboBox::textColourId, juce::Colours::white);
    comboBox.setColour(juce::ComboBox::outlineColourId, juce::Colour(100, 100, 255));
    comboBox.setColour(juce::ComboBox::buttonColourId, juce::Colour(100, 100, 255));
    comboBox.setColour(juce::ComboBox::arrowColourId, juce::Colours::white);
    addAndMakeVisible(comboBox);
}

void EnvelopeComponent::setupButton(juce::TextButton& button, const juce::String& buttonText)
{
    // Set up button
    button.setButtonText(buttonText);
    button.setColour(juce::TextButton::buttonColourId, juce::Colour(60, 60, 100));
    button.setColour(juce::TextButton::buttonOnColourId, juce::Colour(100, 100, 255));
    button.setColour(juce::TextButton::textColourOffId, juce::Colours::white);
    button.setColour(juce::TextButton::textColourOnId, juce::Colours::white);
    addAndMakeVisible(button);
}

void EnvelopeComponent::attackChanged()
{
    if (getEnvelope() != nullptr)
    {
        getEnvelope()->setAttackTime(static_cast<float>(attackSlider.getValue()));
    }
}

void EnvelopeComponent::decayChanged()
{
    if (getEnvelope() != nullptr)
    {
        getEnvelope()->setDecayTime(static_cast<float>(decaySlider.getValue()));
    }
}

void EnvelopeComponent::sustainChanged()
{
    if (getEnvelope() != nullptr)
    {
        getEnvelope()->setSustainLevel(static_cast<float>(sustainSlider.getValue()));
    }
}

void EnvelopeComponent::releaseChanged()
{
    if (getEnvelope() != nullptr)
    {
        getEnvelope()->setReleaseTime(static_cast<float>(releaseSlider.getValue()));
    }
}

void EnvelopeComponent::triggerModeChanged()
{
    if (getEnvelope() != nullptr)
    {
        getEnvelope()->setTriggerMode(static_cast<Envelope::TriggerMode>(triggerModeComboBox.getSelectedId() - 1));
    }
}

void EnvelopeComponent::triggerClicked()
{
    if (getEnvelope() != nullptr)
    {
        getEnvelope()->noteOn();
    }
}

void EnvelopeComponent::releaseClicked()
{
    if (getEnvelope() != nullptr)
    {
        getEnvelope()->noteOff();
    }
}
