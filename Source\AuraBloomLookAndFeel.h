/*
  ==============================================================================

    AuraBloomLookAndFeel.h
    Created: 21 May 2023
    Author:  AuraBloom Team

    This file defines the AuraBloomLookAndFeel class, which customizes the
    appearance of UI components.

  ==============================================================================
*/

#pragma once

#include <JuceHeader.h>

class AuraBloomLookAndFeel : public juce::LookAndFeel_V4
{
public:
    AuraBloomLookAndFeel()
    {
        // Set up colors
        setColour(juce::ResizableWindow::backgroundColourId, juce::Colour(30, 30, 60));
        setColour(juce::TabbedComponent::backgroundColourId, juce::Colour(40, 40, 80));
        setColour(juce::TabbedButtonBar::tabOutlineColourId, juce::Colour(60, 60, 100));
        setColour(juce::TabbedButtonBar::frontOutlineColourId, juce::Colour(100, 100, 255));

        // Slider colors
        setColour(juce::Slider::thumbColourId, juce::Colour(100, 100, 255));
        setColour(juce::Slider::trackColourId, juce::Colour(60, 60, 100));
        setColour(juce::Slider::rotarySliderFillColourId, juce::Colour(100, 100, 255));
        setColour(juce::Slider::rotarySliderOutlineColourId, juce::Colour(60, 60, 100));
        setColour(juce::Slider::textBoxTextColourId, juce::Colours::white);
        setColour(juce::Slider::textBoxOutlineColourId, juce::Colours::transparentBlack);

        // Button colors
        setColour(juce::TextButton::buttonColourId, juce::Colour(60, 60, 100));
        setColour(juce::TextButton::buttonOnColourId, juce::Colour(100, 100, 255));
        setColour(juce::TextButton::textColourOffId, juce::Colours::white);
        setColour(juce::TextButton::textColourOnId, juce::Colours::white);

        // ComboBox colors
        setColour(juce::ComboBox::backgroundColourId, juce::Colour(60, 60, 100));
        setColour(juce::ComboBox::textColourId, juce::Colours::white);
        setColour(juce::ComboBox::outlineColourId, juce::Colour(100, 100, 255));
        setColour(juce::ComboBox::buttonColourId, juce::Colour(100, 100, 255));
        setColour(juce::ComboBox::arrowColourId, juce::Colours::white);

        // PopupMenu colors
        setColour(juce::PopupMenu::backgroundColourId, juce::Colour(40, 40, 80));
        setColour(juce::PopupMenu::textColourId, juce::Colours::white);
        setColour(juce::PopupMenu::highlightedBackgroundColourId, juce::Colour(100, 100, 255));
        setColour(juce::PopupMenu::highlightedTextColourId, juce::Colours::white);
    }

    // Custom rotary slider drawing
    void drawRotarySlider(juce::Graphics& g, int x, int y, int width, int height, float sliderPos,
                         const float rotaryStartAngle, const float rotaryEndAngle, juce::Slider& slider) override
    {
        // Calculate radius and center point
        auto radius = (float)juce::jmin(width / 2, height / 2) - 4.0f;
        auto centerX = (float)x + (float)width * 0.5f;
        auto centerY = (float)y + (float)height * 0.5f;
        auto rx = centerX - radius;
        auto ry = centerY - radius;
        auto rw = radius * 2.0f;
        auto angle = rotaryStartAngle + sliderPos * (rotaryEndAngle - rotaryStartAngle);

        // Draw outer circle
        g.setColour(slider.findColour(juce::Slider::rotarySliderOutlineColourId));
        g.fillEllipse(rx, ry, rw, rw);

        // Draw filled arc
        g.setColour(slider.findColour(juce::Slider::rotarySliderFillColourId));
        juce::Path filledArc;
        filledArc.addPieSegment(rx, ry, rw, rw, rotaryStartAngle, angle, 0.0f);
        g.fillPath(filledArc);

        // Draw pointer
        juce::Path p;
        auto pointerLength = radius * 0.33f;
        auto pointerThickness = 2.0f;
        p.addRectangle(-pointerThickness * 0.5f, -radius, pointerThickness, pointerLength);
        p.applyTransform(juce::AffineTransform::rotation(angle).translated(centerX, centerY));
        g.setColour(juce::Colours::white);
        g.fillPath(p);

        // Draw center dot
        g.setColour(juce::Colours::white);
        g.fillEllipse(centerX - 2.0f, centerY - 2.0f, 4.0f, 4.0f);
    }

    // Custom linear slider drawing
    void drawLinearSlider(juce::Graphics& g, int x, int y, int width, int height,
                         float sliderPos, float minSliderPos, float maxSliderPos,
                         const juce::Slider::SliderStyle style, juce::Slider& slider) override
    {
        if (style == juce::Slider::LinearHorizontal || style == juce::Slider::LinearBar)
        {
            // Draw track
            auto trackWidth = width - 2;
            auto trackHeight = 4.0f;
            auto trackX = x + 1;
            auto trackY = y + (height - trackHeight) * 0.5f;

            g.setColour(slider.findColour(juce::Slider::trackColourId));
            g.fillRoundedRectangle(trackX, trackY, trackWidth, trackHeight, 2.0f);

            // Draw filled part
            auto fillWidth = (int)sliderPos - trackX;
            g.setColour(slider.findColour(juce::Slider::thumbColourId));
            g.fillRoundedRectangle(trackX, trackY, fillWidth, trackHeight, 2.0f);

            // Draw thumb
            auto thumbWidth = 16.0f;
            auto thumbHeight = 16.0f;
            auto thumbX = sliderPos - thumbWidth * 0.5f;
            auto thumbY = trackY + (trackHeight - thumbHeight) * 0.5f;

            g.setColour(slider.findColour(juce::Slider::thumbColourId));
            g.fillEllipse(thumbX, thumbY, thumbWidth, thumbHeight);
            g.setColour(juce::Colours::white);
            g.drawEllipse(thumbX, thumbY, thumbWidth, thumbHeight, 1.0f);
        }
        else
        {
            juce::LookAndFeel_V4::drawLinearSlider(g, x, y, width, height, sliderPos, minSliderPos, maxSliderPos, style, slider);
        }
    }

    // Custom button drawing
    void drawButtonBackground(juce::Graphics& g, juce::Button& button, const juce::Colour& backgroundColour,
                             bool shouldDrawButtonAsHighlighted, bool shouldDrawButtonAsDown) override
    {
        auto bounds = button.getLocalBounds().toFloat().reduced(0.5f, 0.5f);
        auto baseColour = backgroundColour.withMultipliedSaturation(button.hasKeyboardFocus(true) ? 1.3f : 0.9f)
                                         .withMultipliedAlpha(button.isEnabled() ? 1.0f : 0.5f);

        if (shouldDrawButtonAsDown || shouldDrawButtonAsHighlighted)
            baseColour = baseColour.contrasting(shouldDrawButtonAsDown ? 0.2f : 0.05f);

        g.setColour(baseColour);
        g.fillRoundedRectangle(bounds, 4.0f);

        g.setColour(button.findColour(juce::ComboBox::outlineColourId));
        g.drawRoundedRectangle(bounds, 4.0f, 1.0f);
    }
};
