/*
  ==============================================================================

    GrainGenerator.h
    Created: 21 May 2023
    Author:  AuraBloom Team

    This file defines the GrainGenerator class, which is responsible for
    generating and processing grains for granular synthesis.

  ==============================================================================
*/

#pragma once

#include <JuceHeader.h>
#include "AudioBufferManager.h"

/**
 * Represents a single grain in the granular synthesis engine.
 */
struct Grain
{
    bool active = false;
    int startPosition = 0;
    float age = 0.0f;
    float duration = 0.05f; // 50ms default
    float pitch = 1.0f;
    float pan = 0.0f;
    int playDirection = 1; // 1: forward, -1: backward, 0: random
    int envelopeType = 0; // 0: Gaussian, 1: Triangle, 2: Rectangle, 3: Cosine

    /**
     * Check if the grain is still active.
     *
     * @return True if the grain is active
     */
    bool isActive() const
    {
        return active && age < duration;
    }

    /**
     * Get the normalized age of the grain (0.0 to 1.0).
     *
     * @return Normalized age
     */
    float getNormalizedAge() const
    {
        return juce::jlimit(0.0f, 1.0f, age / duration);
    }

    /**
     * Get the envelope value for the current age.
     *
     * @return Envelope value (0.0 to 1.0)
     */
    float getEnvelopeValue() const;
};

/**
 * Generates and processes grains for granular synthesis.
 */
class GrainGenerator
{
public:
    /**
     * Constructor.
     */
    GrainGenerator();

    /**
     * Destructor.
     */
    ~GrainGenerator();

    /**
     * Initialize the grain generator.
     *
     * @param maxGrains Maximum number of simultaneous grains
     * @param sampleRate Sample rate in Hz
     */
    void initialize(int maxGrains, double sampleRate);

    /**
     * Process audio through the grain generator.
     *
     * @param outputBuffer Output audio buffer
     * @param bufferManager Audio buffer manager
     */
    void processAudio(juce::AudioBuffer<float>& outputBuffer, const AudioBufferManager& bufferManager);

    /**
     * Trigger a new grain.
     *
     * @param bufferPosition Position in the buffer (in samples)
     * @param duration Grain duration in seconds
     * @param pitch Pitch multiplier (1.0 = original pitch)
     * @param pan Pan position (-1.0 to 1.0)
     * @param playDirection Playback direction (1: forward, -1: backward, 0: random)
     * @param envelopeType Envelope type (0: Gaussian, 1: Triangle, 2: Rectangle, 3: Cosine)
     * @return True if a grain was triggered, false if all grains are active
     */
    bool triggerGrain(int bufferPosition, float duration, float pitch, float pan, int playDirection, int envelopeType);

    /**
     * Trigger a new grain using normalized buffer position.
     *
     * @param normalizedPosition Normalized position in the buffer (0.0 to 1.0)
     * @param duration Grain duration in seconds
     * @param pitch Pitch multiplier (1.0 = original pitch)
     * @param pan Pan position (-1.0 to 1.0)
     * @param playDirection Playback direction (1: forward, -1: backward, 0: random)
     * @param envelopeType Envelope type (0: Gaussian, 1: Triangle, 2: Rectangle, 3: Cosine)
     * @return True if a grain was triggered, false if all grains are active
     */
    bool triggerGrainNormalized(float normalizedPosition, float duration, float pitch, float pan, int playDirection, int envelopeType);

    /**
     * Set the grain density (grains per second).
     *
     * @param density Grain density
     */
    void setDensity(float density);

    /**
     * Update the grain generator.
     *
     * @param deltaTime Time elapsed since last update in seconds
     * @param bufferManager Audio buffer manager
     */
    void update(float deltaTime, const AudioBufferManager& bufferManager);

    /**
     * Get the number of active grains.
     *
     * @return Number of active grains
     */
    int getActiveGrainCount() const;

    /**
     * Get the maximum number of grains.
     *
     * @return Maximum number of grains
     */
    int getMaxGrains() const;

    /**
     * Clear all active grains.
     * This is useful for cleanup and preventing crashes.
     */
    void clearAllGrains();

private:
    std::vector<Grain> grains;
    double sampleRate = 44100.0;
    float density = 10.0f; // Grains per second
    float timeSinceLastGrain = 0.0f;

    /**
     * Find an inactive grain.
     *
     * @return Index of an inactive grain, or -1 if all grains are active
     */
    int findInactiveGrain() const;

    /**
     * Process a single grain.
     *
     * @param grain The grain to process
     * @param outputBuffer Output audio buffer
     * @param bufferManager Audio buffer manager
     * @param startSample Start sample in the output buffer
     * @param numSamples Number of samples to process
     */
    void processGrain(Grain& grain, juce::AudioBuffer<float>& outputBuffer, const AudioBufferManager& bufferManager, int startSample, int numSamples);

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(GrainGenerator)
};
