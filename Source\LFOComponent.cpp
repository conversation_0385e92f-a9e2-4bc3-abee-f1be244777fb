/*
  ==============================================================================

    LFOComponent.cpp
    Created: 21 May 2023
    Author:  AuraBloom Team

    Implementation of the LFOComponent class.

  ==============================================================================
*/

#include "LFOComponent.h"

LFOComponent::LFOComponent(LFO* lfo)
    : ModulationSourceComponent(lfo)
{
    // Set up waveform combo box
    setupComboBox(waveformComboBox, waveformLabel, "Waveform");
    waveformComboBox.addItem("Sine", LFO::Sine + 1);
    waveformComboBox.addItem("Triangle", LFO::Triangle + 1);
    waveformComboBox.addItem("Square", LFO::Square + 1);
    waveformComboBox.addItem("Sawtooth", LFO::Sawtooth + 1);
    waveformComboBox.addItem("Reverse", LFO::Reverse + 1);
    waveformComboBox.addItem("Random", LFO::Random + 1);
    waveformComboBox.addItem("Noise", LFO::Noise + 1);
    waveformComboBox.setSelectedId(getLFO()->getWaveformType() + 1);
    waveformComboBox.onChange = [this] { waveformChanged(); };

    // Set up frequency slider
    setupSlider(frequencySlider, frequencyLabel, "Frequency");
    frequencySlider.setRange(0.01, 20.0, 0.01);
    frequencySlider.setValue(getLFO()->getFrequency(), juce::dontSendNotification);
    frequencySlider.onValueChange = [this] { frequencyChanged(); };

    // Set up depth slider
    setupSlider(depthSlider, depthLabel, "Depth");
    depthSlider.setRange(0.0, 1.0, 0.01);
    depthSlider.setValue(getLFO()->getDepth(), juce::dontSendNotification);
    depthSlider.onValueChange = [this] { depthChanged(); };

    // Set up phase slider
    setupSlider(phaseSlider, phaseLabel, "Phase");
    phaseSlider.setRange(0.0, 1.0, 0.01);
    phaseSlider.setValue(getLFO()->getPhase(), juce::dontSendNotification);
    phaseSlider.onValueChange = [this] { phaseChanged(); };

    // Set up bipolar toggle
    setupToggle(bipolarToggle, bipolarLabel, "Bipolar");
    bipolarToggle.setToggleState(getLFO()->isBipolar(), juce::dontSendNotification);
    bipolarToggle.onClick = [this] { bipolarChanged(); };

    // Set up sync mode combo box
    setupComboBox(syncModeComboBox, syncModeLabel, "Sync Mode");
    syncModeComboBox.addItem("Free", LFO::Free + 1);
    syncModeComboBox.addItem("Tempo", LFO::Tempo + 1);
    syncModeComboBox.addItem("Note", LFO::Note + 1);
    syncModeComboBox.setSelectedId(getLFO()->getSyncMode() + 1);
    syncModeComboBox.onChange = [this] { syncModeChanged(); };
}

LFOComponent::~LFOComponent()
{
}

void LFOComponent::resized()
{
    auto bounds = getLocalBounds().reduced(4);

    // Header area
    bounds.removeFromTop(30);

    // Waveform display area
    bounds.removeFromTop(bounds.getHeight() / 3);

    // Controls area
    auto controlsArea = bounds;

    // First row: Waveform and Sync Mode
    auto row1 = controlsArea.removeFromTop(50);
    auto waveformArea = row1.removeFromLeft(row1.getWidth() / 2);
    auto syncModeArea = row1;

    waveformLabel.setBounds(waveformArea.removeFromTop(20));
    waveformComboBox.setBounds(waveformArea);

    syncModeLabel.setBounds(syncModeArea.removeFromTop(20));
    syncModeComboBox.setBounds(syncModeArea);

    // Second row: Frequency and Depth
    auto row2 = controlsArea.removeFromTop(50);
    auto frequencyArea = row2.removeFromLeft(row2.getWidth() / 2);
    auto depthArea = row2;

    frequencyLabel.setBounds(frequencyArea.removeFromTop(20));
    frequencySlider.setBounds(frequencyArea);

    depthLabel.setBounds(depthArea.removeFromTop(20));
    depthSlider.setBounds(depthArea);

    // Third row: Phase and Bipolar
    auto row3 = controlsArea.removeFromTop(50);
    auto phaseArea = row3.removeFromLeft(row3.getWidth() / 2);
    auto bipolarArea = row3;

    phaseLabel.setBounds(phaseArea.removeFromTop(20));
    phaseSlider.setBounds(phaseArea);

    bipolarLabel.setBounds(bipolarArea.removeFromTop(20));
    bipolarToggle.setBounds(bipolarArea);
}

void LFOComponent::drawControls(juce::Graphics& g, juce::Rectangle<int> bounds)
{
    // Controls are drawn by the JUCE components
}

LFO* LFOComponent::getLFO() const
{
    return static_cast<LFO*>(source);
}

void LFOComponent::setupComboBox(juce::ComboBox& comboBox, juce::Label& label, const juce::String& labelText)
{
    // Set up label
    label.setText(labelText, juce::dontSendNotification);
    label.setJustificationType(juce::Justification::centred);
    label.setColour(juce::Label::textColourId, juce::Colours::white);
    addAndMakeVisible(label);

    // Set up combo box
    comboBox.setColour(juce::ComboBox::backgroundColourId, juce::Colour(60, 60, 100));
    comboBox.setColour(juce::ComboBox::textColourId, juce::Colours::white);
    comboBox.setColour(juce::ComboBox::outlineColourId, juce::Colour(100, 100, 255));
    comboBox.setColour(juce::ComboBox::buttonColourId, juce::Colour(100, 100, 255));
    comboBox.setColour(juce::ComboBox::arrowColourId, juce::Colours::white);
    addAndMakeVisible(comboBox);
}

void LFOComponent::setupSlider(juce::Slider& slider, juce::Label& label, const juce::String& labelText)
{
    // Set up label
    label.setText(labelText, juce::dontSendNotification);
    label.setJustificationType(juce::Justification::centred);
    label.setColour(juce::Label::textColourId, juce::Colours::white);
    addAndMakeVisible(label);

    // Set up slider
    slider.setSliderStyle(juce::Slider::LinearHorizontal);
    slider.setTextBoxStyle(juce::Slider::TextBoxRight, false, 60, 20);
    slider.setColour(juce::Slider::trackColourId, juce::Colour(60, 60, 100));
    slider.setColour(juce::Slider::thumbColourId, juce::Colour(100, 100, 255));
    slider.setColour(juce::Slider::textBoxTextColourId, juce::Colours::white);
    slider.setColour(juce::Slider::textBoxOutlineColourId, juce::Colours::transparentBlack);
    addAndMakeVisible(slider);
}

void LFOComponent::setupToggle(juce::ToggleButton& toggle, juce::Label& label, const juce::String& labelText)
{
    // Set up label
    label.setText(labelText, juce::dontSendNotification);
    label.setJustificationType(juce::Justification::centred);
    label.setColour(juce::Label::textColourId, juce::Colours::white);
    addAndMakeVisible(label);

    // Set up toggle
    toggle.setColour(juce::ToggleButton::textColourId, juce::Colours::white);
    toggle.setColour(juce::ToggleButton::tickColourId, juce::Colour(100, 100, 255));
    toggle.setColour(juce::ToggleButton::tickDisabledColourId, juce::Colour(60, 60, 100));
    addAndMakeVisible(toggle);
}

void LFOComponent::waveformChanged()
{
    if (getLFO() != nullptr)
    {
        getLFO()->setWaveformType(static_cast<LFO::WaveformType>(waveformComboBox.getSelectedId() - 1));
    }
}

void LFOComponent::frequencyChanged()
{
    if (getLFO() != nullptr)
    {
        getLFO()->setFrequency(static_cast<float>(frequencySlider.getValue()));
    }
}

void LFOComponent::depthChanged()
{
    if (getLFO() != nullptr)
    {
        getLFO()->setDepth(static_cast<float>(depthSlider.getValue()));
    }
}

void LFOComponent::phaseChanged()
{
    if (getLFO() != nullptr)
    {
        getLFO()->setPhase(static_cast<float>(phaseSlider.getValue()));
    }
}

void LFOComponent::bipolarChanged()
{
    if (getLFO() != nullptr)
    {
        getLFO()->setBipolar(bipolarToggle.getToggleState());
    }
}

void LFOComponent::syncModeChanged()
{
    if (getLFO() != nullptr)
    {
        getLFO()->setSyncMode(static_cast<LFO::SyncMode>(syncModeComboBox.getSelectedId() - 1));
    }
}
