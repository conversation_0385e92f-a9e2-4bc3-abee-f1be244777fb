/*
  ==============================================================================

    MacroControlPanel.cpp
    Created: 21 May 2023
    Author:  AuraBloom Team

    宏控制面板的实现

  ==============================================================================
*/

#include "MacroControlPanel.h"

MacroControlPanel::MacroControlPanel()
    : genesisKnob(MacroControlKnob::Genesis, "Genesis")
    , scatterKnob(MacroControlKnob::Scatter, "Scatter")
    , formKnob(MacroControlKnob::Form, "Form")
    , chaosKnob(MacroControlKnob::Chaos, "Chaos")
{
    setupKnobs();
    setupPresetBrowser();
    initializePresets();
}

MacroControlPanel::~MacroControlPanel()
{
}

void MacroControlPanel::paint(juce::Graphics& g)
{
    drawBackground(g);
}

void MacroControlPanel::drawBackground(juce::Graphics& g)
{
    auto bounds = getLocalBounds().toFloat();

    // 深色渐变背景
    juce::ColourGradient gradient(
        juce::Colour(25, 25, 35),
        bounds.getCentreX(), bounds.getY(),
        juce::Colour(35, 25, 45),
        bounds.getCentreX(), bounds.getBottom(),
        false
    );

    g.setGradientFill(gradient);
    g.fillRoundedRectangle(bounds, 8.0f);

    // 边框
    g.setColour(juce::Colour(80, 80, 100));
    g.drawRoundedRectangle(bounds, 8.0f, 1.5f);

    // 内部分隔线
    g.setColour(juce::Colour(60, 60, 80));
    float knobWidth = (bounds.getWidth() - 16) / 4.0f; // 考虑边距

    for (int i = 1; i < 4; ++i)
    {
        float x = bounds.getX() + 8 + knobWidth * i; // 考虑边距
        g.drawVerticalLine(static_cast<int>(x), bounds.getY() + 40, bounds.getBottom() - 10);
    }
}

void MacroControlPanel::drawTitle(juce::Graphics& g)
{
    auto bounds = getLocalBounds();
    auto titleArea = bounds.removeFromTop(30);

    g.setColour(juce::Colours::white.withAlpha(0.8f));
    g.setFont(juce::Font(14.0f, juce::Font::bold));
    g.drawText("MACRO CONTROLS", titleArea, juce::Justification::centred);
}

void MacroControlPanel::resized()
{
    auto bounds = getLocalBounds();
    bounds.reduce(8, 8);    // 减少边距

    // 上方预设浏览器区域 (28像素高度)
    auto presetArea = bounds.removeFromTop(28);

    // 预设区域布局：标签 + 下拉框 + 保存按钮
    presetLabel.setBounds(presetArea.removeFromLeft(55));
    presetArea.removeFromLeft(8); // 间距
    savePresetButton.setBounds(presetArea.removeFromRight(45));
    presetArea.removeFromRight(8); // 间距
    presetComboBox.setBounds(presetArea);

    bounds.removeFromTop(12); // 间距

    // 下方旋钮区域 - 给旋钮更多空间，减少边距
    float knobWidth = bounds.getWidth() / 4.0f;

    genesisKnob.setBounds(bounds.removeFromLeft(static_cast<int>(knobWidth)).reduced(3));
    scatterKnob.setBounds(bounds.removeFromLeft(static_cast<int>(knobWidth)).reduced(3));
    formKnob.setBounds(bounds.removeFromLeft(static_cast<int>(knobWidth)).reduced(3));
    chaosKnob.setBounds(bounds.reduced(3));
}

void MacroControlPanel::setupKnobs()
{
    // 添加所有旋钮到组件
    addAndMakeVisible(genesisKnob);
    addAndMakeVisible(scatterKnob);
    addAndMakeVisible(formKnob);
    addAndMakeVisible(chaosKnob);

    // 设置初始值
    genesisKnob.setValue(0.5f, false);
    scatterKnob.setValue(0.3f, false);
    formKnob.setValue(0.4f, false);
    chaosKnob.setValue(0.2f, false);
}

void MacroControlPanel::setupPresetBrowser()
{
    // 预设标签
    presetLabel.setText("Presets", juce::dontSendNotification);
    presetLabel.setJustificationType(juce::Justification::centred);
    presetLabel.setColour(juce::Label::textColourId, juce::Colours::white);
    presetLabel.setFont(juce::Font(12.0f, juce::Font::bold));
    addAndMakeVisible(presetLabel);

    // 预设下拉框
    presetComboBox.setTextWhenNothingSelected("Select Preset...");
    presetComboBox.setColour(juce::ComboBox::backgroundColourId, juce::Colour(60, 60, 100));
    presetComboBox.setColour(juce::ComboBox::textColourId, juce::Colours::white);
    presetComboBox.setColour(juce::ComboBox::arrowColourId, juce::Colours::lightgrey);
    presetComboBox.onChange = [this]() {
        auto selectedId = presetComboBox.getSelectedId();
        if (selectedId > 0 && selectedId <= static_cast<int>(presets.size()))
        {
            const auto& preset = presets[selectedId - 1];
            loadPreset(preset.name);
        }
    };
    addAndMakeVisible(presetComboBox);

    // 保存按钮
    savePresetButton.setButtonText("Save");
    savePresetButton.setColour(juce::TextButton::buttonColourId, juce::Colour(80, 80, 120));
    savePresetButton.setColour(juce::TextButton::textColourOffId, juce::Colours::white);
    savePresetButton.onClick = [this]() {
        // 简单的保存对话框
        juce::AlertWindow::showAsync(
            juce::MessageBoxOptions()
                .withIconType(juce::MessageBoxIconType::QuestionIcon)
                .withTitle("Save Preset")
                .withMessage("Enter preset name:")
                .withButton("Save")
                .withButton("Cancel"),
            [this](int result) {
                if (result == 1) // Save button
                {
                    // 这里应该获取用户输入的名称，暂时使用默认名称
                    savePreset("User Preset " + juce::String(presets.size() + 1));
                    updatePresetComboBox();
                }
            }
        );
    };
    addAndMakeVisible(savePresetButton);
}

void MacroControlPanel::setParameterChangeCallback(std::function<void(const juce::String&, float)> callback)
{
    genesisKnob.onParameterChange = callback;
    scatterKnob.onParameterChange = callback;
    formKnob.onParameterChange = callback;
    chaosKnob.onParameterChange = callback;
}

void MacroControlPanel::initializePresets()
{
    // 添加一些默认预设
    presets.clear();

    // 基础预设
    presets.emplace_back("Default", 0.5f, 0.3f, 0.4f, 0.2f);
    presets.emplace_back("Gentle", 0.3f, 0.2f, 0.6f, 0.1f);
    presets.emplace_back("Active", 0.7f, 0.5f, 0.3f, 0.4f);
    presets.emplace_back("Extreme", 0.9f, 0.8f, 0.2f, 0.8f);

    // 风格预设
    presets.emplace_back("Ambient", 0.2f, 0.1f, 0.8f, 0.0f);
    presets.emplace_back("Rhythmic", 0.8f, 0.6f, 0.4f, 0.3f);
    presets.emplace_back("Textured", 0.4f, 0.7f, 0.9f, 0.5f);
    presets.emplace_back("Storm", 0.6f, 0.9f, 0.3f, 1.0f);

    // 音乐风格预设
    presets.emplace_back("Classical", 0.3f, 0.2f, 0.7f, 0.1f);
    presets.emplace_back("Electronic", 0.7f, 0.6f, 0.5f, 0.4f);
    presets.emplace_back("Experimental", 0.5f, 0.8f, 0.6f, 0.7f);
    presets.emplace_back("Atmospheric", 0.2f, 0.3f, 0.8f, 0.2f);

    // 更新下拉框
    updatePresetComboBox();
}

void MacroControlPanel::updatePresetComboBox()
{
    presetComboBox.clear();

    for (int i = 0; i < static_cast<int>(presets.size()); ++i)
    {
        presetComboBox.addItem(presets[i].name, i + 1);
    }
}

void MacroControlPanel::savePreset(const juce::String& name)
{
    // 检查是否已存在同名预设
    for (auto& preset : presets)
    {
        if (preset.name == name)
        {
            // 更新现有预设
            preset.genesisValue = genesisKnob.getValue();
            preset.scatterValue = scatterKnob.getValue();
            preset.formValue = formKnob.getValue();
            preset.chaosValue = chaosKnob.getValue();
            updatePresetComboBox();
            return;
        }
    }

    // 添加新预设
    presets.emplace_back(name,
                        genesisKnob.getValue(),
                        scatterKnob.getValue(),
                        formKnob.getValue(),
                        chaosKnob.getValue());
    updatePresetComboBox();
}

void MacroControlPanel::loadPreset(const juce::String& name)
{
    for (const auto& preset : presets)
    {
        if (preset.name == name)
        {
            genesisKnob.setValue(preset.genesisValue);
            scatterKnob.setValue(preset.scatterValue);
            formKnob.setValue(preset.formValue);
            chaosKnob.setValue(preset.chaosValue);
            return;
        }
    }
}

std::vector<juce::String> MacroControlPanel::getPresetNames() const
{
    std::vector<juce::String> names;
    for (const auto& preset : presets)
    {
        names.push_back(preset.name);
    }
    return names;
}
