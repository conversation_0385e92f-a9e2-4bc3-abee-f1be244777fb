/*
  ==============================================================================

    ModulationSource.h
    Created: 21 May 2023
    Author:  AuraBloom Team

    This file defines the ModulationSource base class, which represents a source
    of modulation in the modulation system.

  ==============================================================================
*/

#pragma once

#include <JuceHeader.h>

/**
 * Base class for all modulation sources.
 * 
 * A modulation source generates a signal that can be used to modulate
 * parameters in the plugin. Examples include LFOs, envelopes, and audio
 * followers.
 */
class ModulationSource
{
public:
    /**
     * Constructor.
     * 
     * @param name The name of the modulation source
     */
    ModulationSource(const juce::String& name);
    
    /**
     * Destructor.
     */
    virtual ~ModulationSource();
    
    /**
     * Get the name of the modulation source.
     * 
     * @return The name of the modulation source
     */
    const juce::String& getName() const;
    
    /**
     * Set the name of the modulation source.
     * 
     * @param name The new name of the modulation source
     */
    void setName(const juce::String& name);
    
    /**
     * Get the current value of the modulation source.
     * 
     * @return The current value of the modulation source (typically -1.0 to 1.0 or 0.0 to 1.0)
     */
    float getValue() const;
    
    /**
     * Get whether the modulation source is bipolar.
     * 
     * @return True if the modulation source is bipolar (-1.0 to 1.0), false if unipolar (0.0 to 1.0)
     */
    bool isBipolar() const;
    
    /**
     * Set whether the modulation source is bipolar.
     * 
     * @param bipolar True if the modulation source should be bipolar, false if unipolar
     */
    void setBipolar(bool bipolar);
    
    /**
     * Get whether the modulation source is active.
     * 
     * @return True if the modulation source is active
     */
    bool isActive() const;
    
    /**
     * Set whether the modulation source is active.
     * 
     * @param active True if the modulation source should be active
     */
    void setActive(bool active);
    
    /**
     * Reset the modulation source.
     */
    virtual void reset();
    
    /**
     * Update the modulation source.
     * 
     * @param deltaTime Time elapsed since last update in seconds
     */
    virtual void update(float deltaTime) = 0;
    
    /**
     * Get the modulation value at a specific time offset.
     * 
     * @param timeOffset Time offset in seconds
     * @return The modulation value at the specified time offset
     */
    virtual float getValueAt(float timeOffset) const;
    
    /**
     * Get the modulation depth.
     * 
     * @return The modulation depth (0.0 to 1.0)
     */
    float getDepth() const;
    
    /**
     * Set the modulation depth.
     * 
     * @param depth The modulation depth (0.0 to 1.0)
     */
    void setDepth(float depth);
    
    /**
     * Get the modulation phase.
     * 
     * @return The modulation phase (0.0 to 1.0)
     */
    float getPhase() const;
    
    /**
     * Set the modulation phase.
     * 
     * @param phase The modulation phase (0.0 to 1.0)
     */
    void setPhase(float phase);
    
protected:
    juce::String name;
    float value = 0.0f;
    float depth = 1.0f;
    float phase = 0.0f;
    bool bipolar = true;
    bool active = true;
    
    /**
     * Set the current value of the modulation source.
     * 
     * @param newValue The new value of the modulation source
     */
    void setValue(float newValue);
    
    /**
     * Convert a bipolar value (-1.0 to 1.0) to unipolar (0.0 to 1.0) if needed.
     * 
     * @param bipolarValue The bipolar value to convert
     * @return The converted value
     */
    float convertToUnipolarIfNeeded(float bipolarValue) const;
    
private:
    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(ModulationSource)
};
